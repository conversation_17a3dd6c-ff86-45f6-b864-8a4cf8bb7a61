package main

import (
	"bufio"
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"io"
	"io/fs"
	"log"
	"net"
	"net/http"
	"net/url"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"runtime"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	_ "github.com/mattn/go-sqlite3"
)

// 日志级别定义
type LogLevel int

const (
	LogLevelDEBUG LogLevel = iota
	LogLevelINFO
	LogLevelWARN
	LogLevelERROR
	LogLevelPANIC
)

// 日志级别字符串映射
var logLevelNames = map[LogLevel]string{
	LogLevelDEBUG: "DEBUG",
	LogLevelINFO:  "INFO",
	LogLevelWARN:  "WARN",
	LogLevelERROR: "ERROR",
	LogLevelPANIC: "PANIC",
}

// 全局日志管理器
type GlobalLogger struct {
	logFile     *os.File
	logger      *log.Logger
	logFilePath string
	startTime   time.Time
	version     string
}

// 全局日志实例
var globalLogger *GlobalLogger

// InitGlobalLogger 初始化全局日志管理器
func InitGlobalLogger(version string) error {
	// 获取系统临时目录
	tempDir := os.TempDir()

	// 创建日志文件名
	timestamp := time.Now().Format("20060102-150405")
	logFileName := fmt.Sprintf("augment-cleaner-%s.log", timestamp)
	logFilePath := filepath.Join(tempDir, logFileName)

	// 创建日志文件
	logFile, err := os.Create(logFilePath)
	if err != nil {
		return fmt.Errorf("创建日志文件失败: %v", err)
	}

	// 创建多重写入器（同时写入文件和控制台）
	multiWriter := io.MultiWriter(logFile, os.Stdout)

	// 创建全局日志实例
	globalLogger = &GlobalLogger{
		logFile:     logFile,
		logger:      log.New(multiWriter, "", 0), // 不使用默认前缀，我们自定义
		logFilePath: logFilePath,
		startTime:   time.Now(),
		version:     version,
	}

	// 记录程序启动信息
	globalLogger.logSystemInfo()

	return nil
}

// CloseGlobalLogger 关闭全局日志管理器
func CloseGlobalLogger() {
	if globalLogger != nil && globalLogger.logFile != nil {
		globalLogger.LogInfo("程序结束，关闭日志文件")
		globalLogger.logFile.Close()
	}
}

// logSystemInfo 记录系统信息
func (gl *GlobalLogger) logSystemInfo() {
	gl.LogInfo("=== Augment Cleaner 程序启动 ===")
	gl.LogInfo(fmt.Sprintf("版本: %s", gl.version))
	gl.LogInfo(fmt.Sprintf("操作系统: %s", runtime.GOOS))
	gl.LogInfo(fmt.Sprintf("架构: %s", runtime.GOARCH))
	gl.LogInfo(fmt.Sprintf("Go版本: %s", runtime.Version()))
	gl.LogInfo(fmt.Sprintf("启动时间: %s", gl.startTime.Format("2006-01-02 15:04:05")))
	gl.LogInfo(fmt.Sprintf("日志文件: %s", gl.logFilePath))
	gl.LogInfo(fmt.Sprintf("命令行参数: %v", os.Args))
	gl.LogInfo("=====================================")
}

// Log 通用日志记录方法
func (gl *GlobalLogger) Log(level LogLevel, message string) {
	if gl == nil || gl.logger == nil {
		return
	}

	timestamp := time.Now().Format("2006-01-02 15:04:05.000")
	levelName := logLevelNames[level]
	logMessage := fmt.Sprintf("[%s] [%s] %s", timestamp, levelName, message)

	gl.logger.Println(logMessage)
}

// LogDebug 记录调试信息
func (gl *GlobalLogger) LogDebug(message string) {
	gl.Log(LogLevelDEBUG, message)
}

// LogInfo 记录信息
func (gl *GlobalLogger) LogInfo(message string) {
	gl.Log(LogLevelINFO, message)
}

// LogWarn 记录警告
func (gl *GlobalLogger) LogWarn(message string) {
	gl.Log(LogLevelWARN, message)
}

// LogError 记录错误
func (gl *GlobalLogger) LogError(message string) {
	gl.Log(LogLevelERROR, message)
}

// LogPanic 记录panic信息
func (gl *GlobalLogger) LogPanic(message string) {
	gl.Log(LogLevelPANIC, message)
}

// LogPanicWithStack 记录panic信息和堆栈跟踪
func (gl *GlobalLogger) LogPanicWithStack(panicValue interface{}) {
	// 获取堆栈跟踪
	buf := make([]byte, 4096)
	stackSize := runtime.Stack(buf, false)
	stackTrace := string(buf[:stackSize])

	gl.LogPanic(fmt.Sprintf("程序发生panic: %v", panicValue))
	gl.LogPanic("堆栈跟踪:")
	gl.LogPanic(stackTrace)
}

// GetLogFilePath 获取日志文件路径
func (gl *GlobalLogger) GetLogFilePath() string {
	if gl == nil {
		return ""
	}
	return gl.logFilePath
}

// 全局日志记录函数（便于在其他地方调用）
func LogDebug(message string) {
	if globalLogger != nil {
		globalLogger.LogDebug(message)
	}
}

func LogInfo(message string) {
	if globalLogger != nil {
		globalLogger.LogInfo(message)
	}
}

func LogWarn(message string) {
	if globalLogger != nil {
		globalLogger.LogWarn(message)
	}
}

func LogError(message string) {
	if globalLogger != nil {
		globalLogger.LogError(message)
	}
}

func LogPanic(message string) {
	if globalLogger != nil {
		globalLogger.LogPanic(message)
	}
}

// JetBrains产品定义
type JetBrainsProduct struct {
	Name        string
	DisplayName string
	ConfigDir   string
	Supported   bool
}

// 清理配置
type CleanerConfig struct {
	DryRun         bool
	Verbose        bool
	SafeMode       bool
	SilentMode     bool
	SelectedIDE    string
	CleanChatOnly  bool
	KeepSettings   bool
	CreateBackups  bool // 新增：是否创建备份
	LockFiles      bool // 新增：是否锁定文件
	CleanDatabases bool // 新增：是否清理数据库
	MaxBackups     int  // 新增：最大备份数量
}

// 清理统计
type CleanStats struct {
	FilesDeleted            int
	DirsDeleted             int
	ConfigsCleaned          int
	CachesCleared           int
	ProjectsCleaned         int
	RegistryCleaned         int
	AppDataCleaned          int
	AugmentDirCleaned       int
	ChatProjectsCleaned     int // 新增：清理的聊天项目数量
	ChatFilesDeleted        int // 新增：删除的聊天文件数量
	EditorWorkspacesCleaned int // 新增：清理的编辑器工作区数量
	RecordsCleaned          int // 新增：清理的数据库记录数
	BackupsCreated          int // 新增：创建的备份数量
	FilesLocked             int // 新增：锁定的文件数量
	DatabasesCleaned        int // 新增：清理的数据库数量
	StartTime               time.Time
	EndTime                 time.Time
}

// 备份管理器 - 集成自V2
type BackupManager struct {
	BackupDir string
}

// JetBrains配置常量 - 集成自V2
var jetbrainsConfig = struct {
	IDFiles          []string
	AugmentPatterns  []string
	DatabasePatterns []string
	CacheDirs        []string
}{
	IDFiles:          []string{"PermanentDeviceId", "PermanentUserId"},
	AugmentPatterns:  []string{"%augment%", "%Augment%", "%AUGMENT%", "%device%", "%user%", "%machine%", "%telemetry%"},
	DatabasePatterns: []string{"*.db", "*.sqlite", "*.sqlite3"},
	CacheDirs:        []string{"caches", "logs", "system", "temp"},
}

// VSCode配置常量 - 集成自V2
var vscodeConfig = struct {
	TelemetryKeys []string
	Variants      []string
	DatabaseFiles []string
}{
	TelemetryKeys: []string{"telemetry.machineId", "telemetry.devDeviceId", "telemetry.macMachineId", "telemetry.sqmId"},
	Variants:      []string{"Code", "Code - Insiders", "VSCodium", "Cursor", "code-server"},
	DatabaseFiles: []string{"state.vscdb", "state.vscdb.backup"},
}

// 主清理器
type AugmentCleaner struct {
	Config        CleanerConfig
	Stats         CleanStats
	Products      []JetBrainsProduct
	BasePaths     map[string]string
	BackupManager *BackupManager // 新增：备份管理器
}

// 域名测速结果
type DomainSpeedResult struct {
	Domain  string
	IP      string
	Latency time.Duration
	Success bool
	Error   string
}

// 代理配置
type ProxyConfig struct {
	Type    string // "http", "socks5", "none"
	Address string // "127.0.0.1:7890"
	Enabled bool
}

// 聊天清理相关结构体 - 集成自 augment_chat_cleaner.go

// ChatProjectInfo 聊天项目信息结构
type ChatProjectInfo struct {
	ProjectHash  string
	StorePath    string
	LastModified time.Time
	TotalSize    int64
	ChatFiles    []string
	ImageFiles   []string
	MemoryFiles  []string
}

// ChatCleanOptions 聊天清理选项配置
type ChatCleanOptions struct {
	BackupBeforeClean bool           // 清理前是否备份
	CleanChatHistory  bool           // 是否清理聊天历史
	CleanImages       bool           // 是否清理图片资源
	CleanMemories     bool           // 是否清理记忆文件
	DryRun            bool           // 是否为试运行模式
	TargetProjects    []string       // 指定清理的项目hash列表，空则清理所有
	OlderThan         *time.Duration // 只清理指定时间之前的文件
}

// ChatCleaner 聊天清理器接口
type ChatCleaner interface {
	GetAugmentBaseDir() (string, error)
	ScanChatProjects() ([]ChatProjectInfo, error)
	CleanChatProject(projectHash string, options ChatCleanOptions) error
	CleanAllChatProjects(options ChatCleanOptions) error
	BackupChatProject(projectHash string, backupDir string) error
}

// chatCleaner 聊天清理器实现
type chatCleaner struct {
	baseDir string
	cleaner *AugmentCleaner // 引用主清理器以使用其日志功能
}

// API测速器配置 - 控制API测速行为（已禁用hosts写入功能）
type APIOptimizerConfig struct {
	DryRun     bool   // 干运行模式，只显示操作不实际执行
	Verbose    bool   // 详细输出模式，显示更多调试信息
	SilentMode bool   // 静默模式，减少输出信息
	ProxyType  string // 代理类型: "clash", "v2ray", "custom", "none"
	ProxyAddr  string // 自定义代理地址，格式: host:port
}

// API测速器 - 独立的API测速模块（已禁用hosts文件写入功能）
// 功能包括：域名测速、代理配置（不再包含hosts文件操作）
type APIOptimizer struct {
	Config  APIOptimizerConfig  // 测速器配置
	Proxy   *ProxyConfig        // 代理配置信息
	Results []DomainSpeedResult // 域名测速结果
}

// JetBrains产品列表
var jetbrainsProducts = []JetBrainsProduct{
	{"IntelliJIdea", "IntelliJ IDEA Professional", "IntelliJIdea", true},
	{"IdeaIC", "IntelliJ IDEA Community Edition", "IdeaIC", true},
	{"PyCharm", "PyCharm", "PyCharm", true},
	{"WebStorm", "WebStorm", "WebStorm", true},
	{"PhpStorm", "PhpStorm", "PhpStorm", true},
	{"RubyMine", "RubyMine", "RubyMine", true},
	{"CLion", "CLion", "CLion", true},
	{"DataGrip", "DataGrip", "DataGrip", true},
	{"GoLand", "GoLand", "GoLand", true},
	{"Rider", "Rider", "Rider", true},
	{"AppCode", "AppCode", "AppCode", false}, // 已停止开发
	{"AndroidStudio", "Android Studio", "AndroidStudio", true},
	{"Fleet", "Fleet", "Fleet", false}, // 架构不同
}

// Augment相关的配置属性模式
var augmentPatterns = []string{
	"augment\\.",
	"Augment\\.",
	"AUGMENT_",
	"sessionId",
	"SessionId",
	"installationId",
	"chat\\.history",
	"conversation\\.",
	"feedback\\.",
	"memory\\.",
	"context\\.",
	"preferences\\.augment",
}

// 需要清理的文件模式
var cleanupPatterns = []string{
	"*augment*",
	"*Augment*",
	"*session*",
	"*Session*",
	"chat_history.*",
	"conversations.*",
	"augment.xml",
	"augment.log",
	"*.augment.cache",
}

func NewAugmentCleaner() *AugmentCleaner {
	cleaner := &AugmentCleaner{
		Config: CleanerConfig{
			SafeMode:       true,
			Verbose:        false,
			DryRun:         false,
			SilentMode:     false,
			CleanChatOnly:  false,
			KeepSettings:   false,
			CreateBackups:  true,  // 新增：默认创建备份
			LockFiles:      false, // 新增：默认不锁定文件
			CleanDatabases: true,  // 新增：默认清理数据库
			MaxBackups:     10,    // 新增：最多保留10个备份
		},
		Stats: CleanStats{
			StartTime: time.Now(),
		},
		Products:      jetbrainsProducts,
		BasePaths:     make(map[string]string),
		BackupManager: NewBackupManager(), // 新增：初始化备份管理器
	}

	cleaner.initBasePaths()
	return cleaner
}

// NewChatCleaner 创建新的聊天清理器实例
func NewChatCleaner(mainCleaner *AugmentCleaner) (ChatCleaner, error) {
	cleaner := &chatCleaner{cleaner: mainCleaner}

	baseDir, err := cleaner.GetAugmentBaseDir()
	if err != nil {
		return nil, fmt.Errorf("failed to get augment base directory: %w", err)
	}

	cleaner.baseDir = baseDir
	return cleaner, nil
}

// GetAugmentBaseDir 跨平台获取 Augment 基础目录
func (c *chatCleaner) GetAugmentBaseDir() (string, error) {
	// 优先检查环境变量 AUGMENT_STATE_HOME
	if augmentHome := os.Getenv("AUGMENT_STATE_HOME"); augmentHome != "" {
		c.cleaner.log(fmt.Sprintf("使用环境变量 AUGMENT_STATE_HOME: %s", augmentHome))
		return augmentHome, nil
	}

	// 获取用户主目录
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "", fmt.Errorf("failed to get user home directory: %w", err)
	}

	if homeDir == "" {
		return "", fmt.Errorf("user home directory is empty")
	}

	// 跨平台路径构建
	var augmentDir string
	switch runtime.GOOS {
	case "windows":
		// Windows: C:\Users\<USER>\.augmentcode
		augmentDir = filepath.Join(homeDir, ".augmentcode")
	case "darwin", "linux":
		// macOS/Linux: /Users/<USER>/.augmentcode 或 /home/<USER>/.augmentcode
		augmentDir = filepath.Join(homeDir, ".augmentcode")
	default:
		// 其他系统默认处理
		augmentDir = filepath.Join(homeDir, ".augmentcode")
	}

	c.cleaner.log(fmt.Sprintf("Augment 基础目录: %s", augmentDir))
	return augmentDir, nil
}

// ScanChatProjects 扫描所有项目的聊天记录
func (c *chatCleaner) ScanChatProjects() ([]ChatProjectInfo, error) {
	projectsDir := filepath.Join(c.baseDir, "projects")

	// 检查 projects 目录是否存在
	if _, err := os.Stat(projectsDir); os.IsNotExist(err) {
		c.cleaner.warn(fmt.Sprintf("Projects 目录不存在: %s", projectsDir))
		return []ChatProjectInfo{}, nil
	}

	// 读取所有项目目录
	entries, err := os.ReadDir(projectsDir)
	if err != nil {
		return nil, fmt.Errorf("failed to read projects directory: %w", err)
	}

	var projects []ChatProjectInfo
	for _, entry := range entries {
		if !entry.IsDir() {
			continue
		}

		projectHash := entry.Name()
		pluginStoreDir := filepath.Join(projectsDir, projectHash, "plugin-file-store")

		// 检查 plugin-file-store 目录是否存在
		if _, err := os.Stat(pluginStoreDir); os.IsNotExist(err) {
			c.cleaner.verbose(fmt.Sprintf("跳过项目 %s: 无 plugin-file-store 目录", projectHash))
			continue
		}

		// 扫描项目文件
		projectInfo, err := c.scanChatProjectFiles(projectHash, pluginStoreDir)
		if err != nil {
			c.cleaner.error(fmt.Sprintf("扫描项目 %s 失败: %v", projectHash, err))
			continue
		}

		projects = append(projects, projectInfo)
	}

	c.cleaner.log(fmt.Sprintf("发现 %d 个包含聊天数据的项目", len(projects)))
	return projects, nil
}

// scanChatProjectFiles 扫描单个项目的文件
func (c *chatCleaner) scanChatProjectFiles(projectHash, storePath string) (ChatProjectInfo, error) {
	project := ChatProjectInfo{
		ProjectHash: projectHash,
		StorePath:   storePath,
		ChatFiles:   []string{},
		ImageFiles:  []string{},
		MemoryFiles: []string{},
	}

	err := filepath.Walk(storePath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.IsDir() {
			return nil
		}

		// 更新最后修改时间和总大小
		if info.ModTime().After(project.LastModified) {
			project.LastModified = info.ModTime()
		}
		project.TotalSize += info.Size()

		// 根据文件名和扩展名分类
		fileName := strings.ToLower(info.Name())
		switch {
		case strings.Contains(fileName, "chat") || strings.Contains(fileName, "history"):
			project.ChatFiles = append(project.ChatFiles, path)
		case strings.Contains(fileName, "memories") || strings.HasSuffix(fileName, ".md"):
			project.MemoryFiles = append(project.MemoryFiles, path)
		case c.isImageFile(fileName):
			project.ImageFiles = append(project.ImageFiles, path)
		default:
			// 其他文件也归类到聊天文件中
			project.ChatFiles = append(project.ChatFiles, path)
		}

		return nil
	})

	return project, err
}

// isImageFile 判断是否为图片文件
func (c *chatCleaner) isImageFile(fileName string) bool {
	imageExts := []string{".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg"}
	for _, ext := range imageExts {
		if strings.HasSuffix(fileName, ext) {
			return true
		}
	}
	return false
}

// CleanChatProject 清理指定项目的聊天记录
func (c *chatCleaner) CleanChatProject(projectHash string, options ChatCleanOptions) error {
	c.cleaner.log(fmt.Sprintf("开始清理项目聊天记录: %s", projectHash))

	// 获取项目信息
	projects, err := c.ScanChatProjects()
	if err != nil {
		return fmt.Errorf("failed to scan projects: %w", err)
	}

	var targetProject *ChatProjectInfo
	for _, project := range projects {
		if project.ProjectHash == projectHash {
			targetProject = &project
			break
		}
	}

	if targetProject == nil {
		return fmt.Errorf("project not found: %s", projectHash)
	}

	// 备份处理
	if options.BackupBeforeClean {
		backupDir := filepath.Join(c.baseDir, "backups", fmt.Sprintf("chat_%s_%d", projectHash, time.Now().Unix()))
		if err := c.BackupChatProject(projectHash, backupDir); err != nil {
			return fmt.Errorf("backup failed: %w", err)
		}
		c.cleaner.log(fmt.Sprintf("聊天数据备份完成: %s", backupDir))
	}

	// 执行清理
	return c.executeChatCleanup(*targetProject, options)
}

// CleanAllChatProjects 清理所有项目的聊天记录
func (c *chatCleaner) CleanAllChatProjects(options ChatCleanOptions) error {
	projects, err := c.ScanChatProjects()
	if err != nil {
		return fmt.Errorf("failed to scan projects: %w", err)
	}

	c.cleaner.log(fmt.Sprintf("开始清理 %d 个项目的聊天记录", len(projects)))

	var errors []string
	for _, project := range projects {
		// 如果指定了目标项目列表，则只清理指定项目
		if len(options.TargetProjects) > 0 {
			found := false
			for _, target := range options.TargetProjects {
				if project.ProjectHash == target {
					found = true
					break
				}
			}
			if !found {
				continue
			}
		}

		if err := c.CleanChatProject(project.ProjectHash, options); err != nil {
			errorMsg := fmt.Sprintf("failed to clean project %s: %v", project.ProjectHash, err)
			errors = append(errors, errorMsg)
			c.cleaner.error(errorMsg)
		} else {
			c.cleaner.Stats.ChatProjectsCleaned++
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("cleanup completed with errors: %s", strings.Join(errors, "; "))
	}

	c.cleaner.success("所有项目聊天记录清理完成")
	return nil
}

// BackupChatProject 备份项目聊天数据
func (c *chatCleaner) BackupChatProject(projectHash, backupDir string) error {
	sourceDir := filepath.Join(c.baseDir, "projects", projectHash, "plugin-file-store")

	// 创建备份目录
	if err := os.MkdirAll(backupDir, 0755); err != nil {
		return fmt.Errorf("failed to create backup directory: %w", err)
	}

	// 递归复制文件
	return filepath.Walk(sourceDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 计算相对路径
		relPath, err := filepath.Rel(sourceDir, path)
		if err != nil {
			return err
		}

		destPath := filepath.Join(backupDir, relPath)

		if info.IsDir() {
			return os.MkdirAll(destPath, info.Mode())
		}

		// 复制文件
		return c.copyChatFile(path, destPath)
	})
}

// executeChatCleanup 执行具体的聊天清理操作
func (c *chatCleaner) executeChatCleanup(project ChatProjectInfo, options ChatCleanOptions) error {
	var filesToDelete []string

	// 根据选项确定要删除的文件
	if options.CleanChatHistory {
		filesToDelete = append(filesToDelete, project.ChatFiles...)
	}
	if options.CleanImages {
		filesToDelete = append(filesToDelete, project.ImageFiles...)
	}
	if options.CleanMemories {
		filesToDelete = append(filesToDelete, project.MemoryFiles...)
	}

	// 时间过滤
	if options.OlderThan != nil {
		cutoffTime := time.Now().Add(-*options.OlderThan)
		filesToDelete = c.filterChatFilesByTime(filesToDelete, cutoffTime)
	}

	c.cleaner.log(fmt.Sprintf("项目 %s: %d 个文件将被删除", project.ProjectHash, len(filesToDelete)))

	// 试运行模式
	if options.DryRun {
		for _, file := range filesToDelete {
			c.cleaner.verbose(fmt.Sprintf("[DRY-RUN] 将删除聊天文件: %s", file))
		}
		return nil
	}

	// 执行删除
	var errors []string
	for _, file := range filesToDelete {
		if err := os.Remove(file); err != nil {
			errorMsg := fmt.Sprintf("failed to delete %s: %v", file, err)
			errors = append(errors, errorMsg)
			c.cleaner.error(errorMsg)
		} else {
			c.cleaner.verbose(fmt.Sprintf("删除聊天文件: %s", file))
			c.cleaner.Stats.FilesDeleted++
			c.cleaner.Stats.ChatFilesDeleted++
		}
	}

	// 清理空目录
	c.cleanEmptyChatDirectories(project.StorePath)

	if len(errors) > 0 {
		return fmt.Errorf("cleanup completed with errors: %s", strings.Join(errors, "; "))
	}

	return nil
}

// filterChatFilesByTime 根据时间过滤聊天文件
func (c *chatCleaner) filterChatFilesByTime(files []string, cutoffTime time.Time) []string {
	var filtered []string
	for _, file := range files {
		if info, err := os.Stat(file); err == nil {
			if info.ModTime().Before(cutoffTime) {
				filtered = append(filtered, file)
			}
		}
	}
	return filtered
}

// cleanEmptyChatDirectories 清理空的聊天目录
func (c *chatCleaner) cleanEmptyChatDirectories(rootDir string) {
	filepath.Walk(rootDir, func(path string, info os.FileInfo, err error) error {
		if err != nil || !info.IsDir() || path == rootDir {
			return err
		}

		// 检查目录是否为空
		if entries, err := os.ReadDir(path); err == nil && len(entries) == 0 {
			if err := os.Remove(path); err == nil {
				c.cleaner.verbose(fmt.Sprintf("删除空的聊天目录: %s", path))
				c.cleaner.Stats.DirsDeleted++
			}
		}

		return nil
	})
}

// copyChatFile 复制聊天文件的辅助函数
func (c *chatCleaner) copyChatFile(src, dst string) error {
	// 创建目标目录
	if err := os.MkdirAll(filepath.Dir(dst), 0755); err != nil {
		return err
	}

	// 打开源文件
	srcFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer srcFile.Close()

	// 创建目标文件
	dstFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer dstFile.Close()

	// 复制文件内容
	_, err = io.Copy(dstFile, srcFile)
	if err != nil {
		return err
	}

	// 复制文件权限
	srcInfo, err := os.Stat(src)
	if err != nil {
		return err
	}

	return os.Chmod(dst, srcInfo.Mode())
}

// NewAPIOptimizer 创建新的API测速器实例
func NewAPIOptimizer() *APIOptimizer {
	return &APIOptimizer{
		Config: APIOptimizerConfig{
			DryRun:     false,
			Verbose:    false,
			SilentMode: false,
			ProxyType:  "none",
		},
		Results: make([]DomainSpeedResult, 0),
	}
}

// SetConfig 设置API测速器配置
func (a *APIOptimizer) SetConfig(dryRun, verbose, silent bool) {
	a.Config.DryRun = dryRun
	a.Config.Verbose = verbose
	a.Config.SilentMode = silent
}

// log 输出日志信息
func (a *APIOptimizer) log(message string) {
	// 记录到全局日志
	LogInfo(fmt.Sprintf("[API] %s", message))

	if !a.Config.SilentMode {
		timestamp := time.Now().Format("15:04:05")
		fmt.Printf("[%s] %s\n", timestamp, message)
	}
}

// verbose 输出详细信息
func (a *APIOptimizer) verbose(message string) {
	// 记录到全局日志
	LogDebug(fmt.Sprintf("[API] %s", message))

	if a.Config.Verbose && !a.Config.SilentMode {
		fmt.Printf("  → %s\n", message)
	}
}

// warn 输出警告信息
func (a *APIOptimizer) warn(message string) {
	// 记录到全局日志
	LogWarn(fmt.Sprintf("[API] %s", message))

	if !a.Config.SilentMode {
		fmt.Printf("⚠️  %s\n", message)
	}
}

// success 输出成功信息
func (a *APIOptimizer) success(message string) {
	// 记录到全局日志
	LogInfo(fmt.Sprintf("[API] SUCCESS: %s", message))

	if !a.Config.SilentMode {
		fmt.Printf("✅ %s\n", message)
	}
}

// error 输出错误信息
func (a *APIOptimizer) error(message string) {
	// 记录到全局日志
	LogError(fmt.Sprintf("[API] ERROR: %s", message))

	fmt.Printf("❌ %s\n", message)
}

func (c *AugmentCleaner) initBasePaths() {
	homeDir, _ := os.UserHomeDir()

	switch runtime.GOOS {
	case "windows":
		appData := os.Getenv("APPDATA")
		localAppData := os.Getenv("LOCALAPPDATA")
		if appData == "" {
			appData = filepath.Join(homeDir, "AppData", "Roaming")
		}
		if localAppData == "" {
			localAppData = filepath.Join(homeDir, "AppData", "Local")
		}
		c.BasePaths["config"] = filepath.Join(appData, "JetBrains")
		c.BasePaths["cache"] = filepath.Join(localAppData, "JetBrains")
		c.BasePaths["logs"] = filepath.Join(localAppData, "JetBrains")
	case "darwin":
		c.BasePaths["config"] = filepath.Join(homeDir, "Library", "Application Support", "JetBrains")
		c.BasePaths["cache"] = filepath.Join(homeDir, "Library", "Caches", "JetBrains")
		c.BasePaths["logs"] = filepath.Join(homeDir, "Library", "Logs", "JetBrains")
		c.BasePaths["preferences"] = filepath.Join(homeDir, "Library", "Preferences", "JetBrains")
	default: // Linux
		c.BasePaths["config"] = filepath.Join(homeDir, ".config", "JetBrains")
		c.BasePaths["cache"] = filepath.Join(homeDir, ".cache", "JetBrains")
		c.BasePaths["share"] = filepath.Join(homeDir, ".local", "share", "JetBrains")
	}
}

func (c *AugmentCleaner) log(message string) {
	// 记录到全局日志
	LogInfo(fmt.Sprintf("[Cleaner] %s", message))

	if !c.Config.SilentMode {
		timestamp := time.Now().Format("15:04:05")
		fmt.Printf("[%s] %s\n", timestamp, message)
	}
}

func (c *AugmentCleaner) verbose(message string) {
	// 记录到全局日志
	LogDebug(fmt.Sprintf("[Cleaner] %s", message))

	if c.Config.Verbose && !c.Config.SilentMode {
		fmt.Printf("  → %s\n", message)
	}
}

func (c *AugmentCleaner) warn(message string) {
	// 记录到全局日志
	LogWarn(fmt.Sprintf("[Cleaner] %s", message))

	if !c.Config.SilentMode {
		fmt.Printf("⚠️  %s\n", message)
	}
}

func (c *AugmentCleaner) success(message string) {
	// 记录到全局日志
	LogInfo(fmt.Sprintf("[Cleaner] SUCCESS: %s", message))

	if !c.Config.SilentMode {
		fmt.Printf("✅ %s\n", message)
	}
}

func (c *AugmentCleaner) error(message string) {
	// 记录到全局日志
	LogError(fmt.Sprintf("[Cleaner] ERROR: %s", message))

	fmt.Printf("❌ %s\n", message)
}

// 检查IDE是否正在运行
func (c *AugmentCleaner) checkIDERunning() error {
	c.log("检查JetBrains IDE是否正在运行...")

	processes := []string{
		// JetBrains IDEs
		"idea64.exe", "idea.exe", "intellij",
		"pycharm64.exe", "pycharm.exe", "pycharm",
		"webstorm64.exe", "webstorm.exe", "webstorm",
		"phpstorm64.exe", "phpstorm.exe", "phpstorm",
		"rubymine64.exe", "rubymine.exe", "rubymine",
		"clion64.exe", "clion.exe", "clion",
		"datagrip64.exe", "datagrip.exe", "datagrip",
		"goland64.exe", "goland.exe", "goland",
		"rider64.exe", "rider.exe", "rider",
		"studio64.exe", "studio.exe", "android-studio",
		"fleet.exe", "fleet",
		// 编辑器
		"cursor.exe", "cursor", "Cursor",
		"code.exe", "code", "Code",
	}

	for _, proc := range processes {
		if c.isProcessRunning(proc) {
			return fmt.Errorf("检测到 %s 正在运行，请先关闭所有JetBrains IDE", proc)
		}
	}

	c.success("未检测到运行中的JetBrains IDE")
	return nil
}

func (c *AugmentCleaner) isProcessRunning(processName string) bool {
	c.verbose(fmt.Sprintf("检查进程是否运行: %s", processName))

	// 设置超时上下文，避免长时间阻塞
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	var isRunning bool

	// 预处理进程名，确保跨平台兼容性
	processName = c.normalizeProcessName(processName)

	switch runtime.GOOS {
	case "windows":
		isRunning = c.isProcessRunningWindows(ctx, processName)
	case "linux", "darwin":
		isRunning = c.isProcessRunningUnix(ctx, processName)
	default:
		c.verbose(fmt.Sprintf("不支持的操作系统: %s，跳过进程检查", runtime.GOOS))
		return false
	}

	if isRunning {
		c.verbose(fmt.Sprintf("进程 %s 正在运行", processName))
	} else {
		c.verbose(fmt.Sprintf("进程 %s 未运行", processName))
	}

	return isRunning
}

// normalizeProcessName 标准化进程名，确保跨平台兼容性
func (c *AugmentCleaner) normalizeProcessName(processName string) string {
	// 移除路径，只保留文件名
	processName = filepath.Base(processName)

	// 根据操作系统调整进程名
	switch runtime.GOOS {
	case "windows":
		// Windows 需要 .exe 后缀
		if !strings.HasSuffix(strings.ToLower(processName), ".exe") {
			// 特殊处理一些已知的进程名
			switch strings.ToLower(processName) {
			case "cursor":
				return "Cursor.exe"
			case "code":
				return "Code.exe"
			default:
				return processName + ".exe"
			}
		}
		return processName
	case "linux", "darwin":
		// Unix 系统移除 .exe 后缀
		processName = strings.TrimSuffix(processName, ".exe")

		// macOS 特殊处理
		if runtime.GOOS == "darwin" {
			switch strings.ToLower(processName) {
			case "cursor":
				return "Cursor"
			case "code":
				return "Code"
			}
		}

		return processName
	default:
		return processName
	}
}

// Windows平台进程检查
func (c *AugmentCleaner) isProcessRunningWindows(ctx context.Context, processName string) bool {
	// 方法1：使用 tasklist 命令
	if c.checkProcessWithTasklist(ctx, processName) {
		return true
	}

	// 方法2：使用 wmic 命令作为备选
	if c.checkProcessWithWmic(ctx, processName) {
		return true
	}

	return false
}

// Unix平台（Linux/macOS）进程检查
func (c *AugmentCleaner) isProcessRunningUnix(ctx context.Context, processName string) bool {
	// 方法1：使用 pgrep 命令
	if c.checkProcessWithPgrep(ctx, processName) {
		return true
	}

	// 方法2：使用 ps 命令作为备选
	if c.checkProcessWithPs(ctx, processName) {
		return true
	}

	return false
}

// 使用 tasklist 命令检查Windows进程
func (c *AugmentCleaner) checkProcessWithTasklist(ctx context.Context, processName string) bool {
	// 确保进程名有 .exe 后缀（Windows 需要）
	if !strings.HasSuffix(strings.ToLower(processName), ".exe") {
		processName = processName + ".exe"
	}

	cmd := exec.CommandContext(ctx, "tasklist", "/FI", fmt.Sprintf("IMAGENAME eq %s", processName), "/NH")
	output, err := cmd.Output()
	if err != nil {
		c.verbose(fmt.Sprintf("tasklist 命令执行失败: %v", err))
		return false
	}

	outputStr := strings.TrimSpace(string(output))
	if outputStr == "" || strings.Contains(outputStr, "INFO: No tasks are running") {
		return false
	}

	// 检查输出是否包含进程名
	lines := strings.Split(outputStr, "\n")
	for _, line := range lines {
		if strings.Contains(strings.ToLower(line), strings.ToLower(processName)) {
			c.verbose(fmt.Sprintf("tasklist 检测到进程: %s", strings.TrimSpace(line)))
			return true
		}
	}

	return false
}

// 使用 wmic 命令检查Windows进程
func (c *AugmentCleaner) checkProcessWithWmic(ctx context.Context, processName string) bool {
	cmd := exec.CommandContext(ctx, "wmic", "process", "where", fmt.Sprintf("name='%s'", processName), "get", "name", "/format:list")
	output, err := cmd.Output()
	if err != nil {
		c.verbose(fmt.Sprintf("wmic 命令执行失败: %v", err))
		return false
	}

	outputStr := strings.TrimSpace(string(output))
	if strings.Contains(outputStr, fmt.Sprintf("Name=%s", processName)) {
		c.verbose(fmt.Sprintf("wmic 检测到进程: %s", processName))
		return true
	}

	return false
}

// 使用 pgrep 命令检查Unix进程 - 安全修复版本
func (c *AugmentCleaner) checkProcessWithPgrep(ctx context.Context, processName string) bool {
	// 移除 .exe 后缀（如果存在）
	cleanProcessName := strings.TrimSuffix(processName, ".exe")

	// 🚨 安全修复：只使用精确匹配，移除危险的模糊匹配
	patterns := []string{
		cleanProcessName, // 精确匹配
	}

	// 🔒 安全的特殊处理 - 只添加已知安全的精确匹配
	switch strings.ToLower(cleanProcessName) {
	case "cursor":
		patterns = append(patterns, "Cursor")
	case "code":
		patterns = append(patterns, "Code")
	case "intellij":
		patterns = append(patterns, "idea", "IntelliJ")
	}

	for _, pattern := range patterns {
		// 🛡️ 安全检查：使用精确匹配而不是模糊匹配
		if c.isPgrepPatternSafe(pattern) {
			cmd := exec.CommandContext(ctx, "pgrep", "-x", pattern) // 使用 -x 进行精确匹配
			output, err := cmd.Output()
			if err != nil {
				c.verbose(fmt.Sprintf("pgrep 精确匹配失败 (pattern: %s): %v", pattern, err))
				continue
			}

			outputStr := strings.TrimSpace(string(output))
			if outputStr != "" {
				// 🔍 额外验证：确保找到的进程是安全的
				if c.validatePgrepResult(outputStr, pattern) {
					c.verbose(fmt.Sprintf("pgrep 安全检测到进程: %s (pattern: %s, PIDs: %s)", cleanProcessName, pattern, outputStr))
					return true
				}
			}
		} else {
			c.verbose(fmt.Sprintf("🚨 跳过不安全的 pgrep 模式: %s", pattern))
		}
	}

	return false
}

// isPgrepPatternSafe 检查 pgrep 模式是否安全
func (c *AugmentCleaner) isPgrepPatternSafe(pattern string) bool {
	pattern = strings.ToLower(pattern)

	// 🚨 禁止通配符和危险模式
	dangerousPatterns := []string{
		".*", // 通配符
		"*",  // 通配符
		".",  // 点号
	}

	// 检查是否包含危险模式
	for _, dangerous := range dangerousPatterns {
		if strings.Contains(pattern, dangerous) {
			return false
		}
	}

	// 🔒 只允许明确的应用程序名称（白名单机制）
	safePatterns := []string{
		"cursor",
		"code", // 🚨 特殊处理：允许但需要额外验证
		"intellij",
		"idea",
		"pycharm",
		"webstorm",
		"phpstorm",
		"rubymine",
		"clion",
		"datagrip",
		"goland",
		"rider",
		"fleet",
	}

	for _, safe := range safePatterns {
		if pattern == safe {
			return true
		}
	}

	return false
}

// validatePgrepResult 验证 pgrep 结果的安全性
func (c *AugmentCleaner) validatePgrepResult(pidOutput, pattern string) bool {
	pids := strings.Fields(pidOutput)

	for _, pid := range pids {
		// 获取进程详细信息进行验证
		cmd := exec.Command("ps", "-p", pid, "-o", "comm,command")
		output, err := cmd.Output()
		if err != nil {
			c.verbose(fmt.Sprintf("无法验证 PID %s: %v", pid, err))
			continue
		}

		processInfo := strings.ToLower(string(output))

		// 🛡️ 安全检查：确保不是系统进程
		if c.isSystemCriticalProcess(processInfo) {
			c.verbose(fmt.Sprintf("🚨 安全保护：PID %s 是系统关键进程，跳过", pid))
			continue
		}

		// 如果至少有一个安全的进程，返回 true
		return true
	}

	return false
}

// 使用 ps 命令检查Unix进程 - 安全修复版本
func (c *AugmentCleaner) checkProcessWithPs(ctx context.Context, processName string) bool {
	// 移除 .exe 后缀（如果存在）
	cleanProcessName := strings.TrimSuffix(processName, ".exe")

	var cmd *exec.Cmd
	if runtime.GOOS == "darwin" {
		// macOS 使用更详细的 ps 参数，包含完整命令行
		cmd = exec.CommandContext(ctx, "ps", "ax", "-o", "comm,command")
	} else {
		// Linux 使用标准参数
		cmd = exec.CommandContext(ctx, "ps", "-eo", "comm,cmd")
	}

	output, err := cmd.Output()
	if err != nil {
		c.verbose(fmt.Sprintf("ps 命令执行失败: %v", err))
		return false
	}

	outputStr := strings.ToLower(string(output))
	cleanProcessNameLower := strings.ToLower(cleanProcessName)

	lines := strings.Split(outputStr, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)

		// 🚨 安全修复：先检查是否为系统关键进程
		if c.isSystemCriticalProcess(line) {
			continue // 跳过系统进程
		}

		// 🔒 更严格的匹配逻辑
		if c.isProcessLineMatch(line, cleanProcessNameLower) {
			// 🛡️ 双重验证：确保匹配的进程是安全的
			if c.validateProcessMatch(line, cleanProcessNameLower) {
				c.verbose(fmt.Sprintf("ps 安全检测到进程: %s", line))
				return true
			}
		}
	}

	return false
}

// isProcessLineMatch 检查进程行是否匹配目标进程（安全版本）
func (c *AugmentCleaner) isProcessLineMatch(processLine, targetProcess string) bool {
	// 🚨 特别处理危险的 "code" 匹配
	if targetProcess == "code" {
		// 只匹配明确的 VSCode 相关进程，避免匹配 CodesigningHelper
		vscodeIndicators := []string{
			"visual studio code",
			"/applications/visual studio code.app",
			"code.app/contents/macos",
			"/usr/share/code",
			"/opt/visual-studio-code",
		}

		for _, indicator := range vscodeIndicators {
			if strings.Contains(processLine, indicator) {
				return true
			}
		}

		// 检查是否为独立的 "code" 进程（不是其他进程的一部分）
		fields := strings.Fields(processLine)
		if len(fields) > 0 {
			processName := filepath.Base(fields[0])
			return processName == "code"
		}

		return false
	}

	// 对于其他进程，使用更安全的匹配
	return strings.Contains(processLine, targetProcess)
}

// validateProcessMatch 验证进程匹配的准确性 - 安全修复版本
func (c *AugmentCleaner) validateProcessMatch(processLine, targetProcess string) bool {
	processLine = strings.ToLower(processLine)
	targetProcess = strings.ToLower(targetProcess)

	// 分割进程行，获取进程名和命令行
	fields := strings.Fields(processLine)
	if len(fields) == 0 {
		return false
	}

	// 🚨 安全检查：禁止匹配系统关键进程
	if c.isSystemCriticalProcess(processLine) {
		c.verbose(fmt.Sprintf("🛡️ 安全保护：跳过系统关键进程 %s", fields[0]))
		return false
	}

	// 获取进程名（去除路径）
	processName := filepath.Base(fields[0])
	fullCommand := strings.Join(fields, " ")

	// 🔒 精确匹配策略 - 避免误杀系统进程
	return c.isExactProcessMatch(processName, fullCommand, targetProcess)
}

// isSystemCriticalProcess 检查是否为系统关键进程
func (c *AugmentCleaner) isSystemCriticalProcess(processLine string) bool {
	processLine = strings.ToLower(processLine)

	// macOS 系统进程保护列表
	systemProcessPrefixes := []string{
		"com.apple.",        // Apple 系统进程
		"/system/",          // 系统目录进程
		"/usr/libexec/",     // 系统库执行文件
		"/usr/sbin/",        // 系统管理程序
		"kernel_task",       // 内核任务
		"launchd",           // 启动守护进程
		"codesigninghelper", // 🚨 特别保护代码签名助手
		"securityd",         // 安全守护进程
		"trustd",            // 信任守护进程
	}

	// Linux 系统进程保护列表
	linuxSystemProcesses := []string{
		"systemd",
		"kthreadd",
		"init",
		"[", // 内核线程
	}

	// 检查 macOS 系统进程
	if runtime.GOOS == "darwin" {
		for _, prefix := range systemProcessPrefixes {
			if strings.Contains(processLine, prefix) {
				return true
			}
		}
	}

	// 检查 Linux 系统进程
	if runtime.GOOS == "linux" {
		for _, sysProc := range linuxSystemProcesses {
			if strings.Contains(processLine, sysProc) {
				return true
			}
		}
	}

	return false
}

// isExactProcessMatch 精确进程匹配 - 避免误匹配
func (c *AugmentCleaner) isExactProcessMatch(processName, fullCommand, targetProcess string) bool {
	// 🎯 精确匹配策略
	switch targetProcess {
	case "cursor":
		return c.matchCursorProcess(processName, fullCommand)
	case "code":
		return c.matchVSCodeProcess(processName, fullCommand)
	case "intellij":
		return c.matchIntelliJProcess(processName, fullCommand)
	default:
		return c.matchGenericProcess(processName, fullCommand, targetProcess)
	}
}

// matchCursorProcess 精确匹配 Cursor 编辑器
func (c *AugmentCleaner) matchCursorProcess(processName, fullCommand string) bool {
	// 精确匹配 Cursor 进程名
	exactMatches := []string{"cursor", "cursor.app"}
	for _, match := range exactMatches {
		if processName == match {
			return true
		}
	}

	// 检查应用程序路径
	cursorPaths := []string{
		"/applications/cursor.app",
		"cursor.app/contents/macos/cursor",
		"/opt/cursor",
	}

	for _, path := range cursorPaths {
		if strings.Contains(fullCommand, path) {
			return true
		}
	}

	return false
}

// matchVSCodeProcess 精确匹配 Visual Studio Code
func (c *AugmentCleaner) matchVSCodeProcess(processName, fullCommand string) bool {
	// 🚨 严格匹配 - 避免误杀 com.apple.CodesigningHelper

	// 精确进程名匹配
	exactMatches := []string{"code", "code.exe"}
	for _, match := range exactMatches {
		if processName == match {
			return true
		}
	}

	// 检查 VSCode 特定路径和标识
	vscodePaths := []string{
		"/applications/visual studio code.app",
		"visual studio code.app/contents/macos/electron",
		"/opt/visual-studio-code",
		"/usr/share/code",
		"code.app/contents/macos",
	}

	for _, path := range vscodePaths {
		if strings.Contains(fullCommand, path) {
			return true
		}
	}

	// 🛡️ 额外安全检查：确保不是系统进程
	if strings.Contains(fullCommand, "codesigning") ||
		strings.Contains(fullCommand, "com.apple") {
		return false
	}

	return false
}

// matchIntelliJProcess 精确匹配 IntelliJ IDEA
func (c *AugmentCleaner) matchIntelliJProcess(processName, fullCommand string) bool {
	// 精确匹配 IntelliJ 相关进程
	exactMatches := []string{"idea", "intellij", "idea64", "idea.exe"}
	for _, match := range exactMatches {
		if processName == match {
			return true
		}
	}

	// 检查 IntelliJ 应用程序路径
	intellijPaths := []string{
		"intellij idea",
		"jetbrains/toolbox",
		"/applications/intellij idea",
		"idea.app/contents/macos",
	}

	for _, path := range intellijPaths {
		if strings.Contains(fullCommand, path) {
			return true
		}
	}

	return false
}

// matchGenericProcess 通用进程精确匹配
func (c *AugmentCleaner) matchGenericProcess(processName, fullCommand, targetProcess string) bool {
	// 只进行精确的进程名匹配，避免模糊匹配
	return processName == targetProcess
}

// 检查Augment账号登录状态
func (c *AugmentCleaner) checkAugmentLogout() error {
	if c.Config.SilentMode {
		return nil
	}

	c.log("检查Augment账号登录状态...")
	c.warn("⚠️  重要提醒：请确保您已在所有JetBrains IDE中退出Augment账号登录状态")
	c.warn("⚠️  这将确保清理过程更加彻底，避免残留数据")

	if !c.Config.SilentMode {
		reader := bufio.NewReader(os.Stdin)
		fmt.Print("确认已退出Augment账号登录? (y/N): ")
		confirm, _ := reader.ReadString('\n')
		confirm = strings.TrimSpace(strings.ToLower(confirm))

		if confirm != "y" && confirm != "yes" {
			return fmt.Errorf("请先在IDE中退出Augment账号登录，然后重新运行清理工具")
		}
	}

	c.success("✓ 已确认退出Augment账号登录状态")
	return nil
}

// 发现已安装的JetBrains产品
func (c *AugmentCleaner) discoverInstalledProducts() []JetBrainsProduct {
	c.log("扫描已安装的JetBrains产品...")

	var installedProducts []JetBrainsProduct

	for _, basePath := range c.BasePaths {
		if _, err := os.Stat(basePath); os.IsNotExist(err) {
			continue
		}

		entries, err := os.ReadDir(basePath)
		if err != nil {
			continue
		}

		for _, entry := range entries {
			if !entry.IsDir() {
				continue
			}

			dirName := entry.Name()
			for _, product := range c.Products {
				if strings.HasPrefix(dirName, product.ConfigDir) && product.Supported {
					// 检查是否已经添加过
					found := false
					for _, installed := range installedProducts {
						if installed.ConfigDir == dirName {
							found = true
							break
						}
					}
					if !found {
						installedProduct := product
						installedProduct.ConfigDir = dirName
						installedProducts = append(installedProducts, installedProduct)
						c.verbose(fmt.Sprintf("发现: %s (%s)", product.DisplayName, dirName))
					}
				}
			}
		}
	}

	c.log(fmt.Sprintf("发现 %d 个已安装的JetBrains产品", len(installedProducts)))
	return installedProducts
}

// 清理配置文件
func (c *AugmentCleaner) cleanConfigFiles() error {
	c.log("清理配置文件...")

	installedProducts := c.discoverInstalledProducts()

	for _, product := range installedProducts {
		if c.Config.SelectedIDE != "" && !strings.Contains(product.ConfigDir, c.Config.SelectedIDE) {
			continue
		}

		c.verbose(fmt.Sprintf("清理 %s 配置...", product.DisplayName))

		for pathType, basePath := range c.BasePaths {
			productPath := filepath.Join(basePath, product.ConfigDir)
			if _, err := os.Stat(productPath); os.IsNotExist(err) {
				continue
			}

			// 清理options目录
			optionsPath := filepath.Join(productPath, "options")
			if err := c.cleanOptionsDir(optionsPath); err != nil {
				c.warn(fmt.Sprintf("清理 %s options 失败: %v", pathType, err))
			}

			// 清理插件目录
			pluginsPath := filepath.Join(productPath, "plugins", "intellij-augment")
			if err := c.cleanPluginDir(pluginsPath); err != nil {
				c.verbose(fmt.Sprintf("清理 %s 插件目录失败: %v", pathType, err))
			}

			// 清理其他Augment文件
			if err := c.cleanAugmentFiles(productPath); err != nil {
				c.warn(fmt.Sprintf("清理 %s Augment文件失败: %v", pathType, err))
			}
		}

		c.Stats.ConfigsCleaned++
	}

	// 新增：清理数据库文件
	c.log("清理数据库文件...")
	dbFiles := c.findDatabaseFiles()
	for _, dbFile := range dbFiles {
		// 创建备份
		if !c.Config.DryRun {
			backupPath, err := c.BackupManager.CreateFileBackup(dbFile, fmt.Sprintf("db_%s", filepath.Base(dbFile)))
			if err != nil {
				c.warn(fmt.Sprintf("数据库备份失败 %s: %v", dbFile, err))
			} else {
				c.verbose(fmt.Sprintf("数据库已备份: %s", backupPath))
				c.Stats.BackupsCreated++
			}
		}

		// 清理数据库
		recordsCleaned, err := c.cleanSQLiteDatabase(dbFile)
		if err != nil {
			c.warn(fmt.Sprintf("数据库清理失败 %s: %v", dbFile, err))
		} else if recordsCleaned > 0 {
			c.verbose(fmt.Sprintf("数据库清理完成 %s: %d 条记录", dbFile, recordsCleaned))
			c.Stats.RecordsCleaned += recordsCleaned
			c.Stats.DatabasesCleaned++
		}
	}

	c.success(fmt.Sprintf("配置文件清理完成，共处理 %d 个产品，%d 个数据库", c.Stats.ConfigsCleaned, c.Stats.DatabasesCleaned))
	return nil
}

// 清理options目录
func (c *AugmentCleaner) cleanOptionsDir(optionsPath string) error {
	if _, err := os.Stat(optionsPath); os.IsNotExist(err) {
		return nil
	}

	configFiles := []string{"other.xml", "project.default.xml", "ide.general.xml", "editor.xml"}

	for _, configFile := range configFiles {
		filePath := filepath.Join(optionsPath, configFile)
		if err := c.cleanXMLFile(filePath); err != nil {
			c.verbose(fmt.Sprintf("清理 %s 失败: %v", configFile, err))
		}
	}

	// 删除augment.xml
	augmentXML := filepath.Join(optionsPath, "augment.xml")
	if c.Config.DryRun {
		if _, err := os.Stat(augmentXML); err == nil {
			c.verbose(fmt.Sprintf("[DRY-RUN] 将删除: %s", augmentXML))
		}
	} else {
		if err := os.Remove(augmentXML); err == nil {
			c.verbose(fmt.Sprintf("删除: %s", augmentXML))
			c.Stats.FilesDeleted++
		}
	}

	return nil
}

// 清理XML配置文件中的Augment属性
func (c *AugmentCleaner) cleanXMLFile(filePath string) error {
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil
	}

	content, err := os.ReadFile(filePath)
	if err != nil {
		return err
	}

	originalContent := string(content)
	modifiedContent := originalContent

	// 移除Augment相关的属性
	for _, pattern := range augmentPatterns {
		re := regexp.MustCompile(fmt.Sprintf(`\s*<property name="%s[^"]*"[^>]*/>`, pattern))
		modifiedContent = re.ReplaceAllString(modifiedContent, "")
	}

	// 如果内容有变化且不是干运行模式
	if modifiedContent != originalContent {
		if c.Config.DryRun {
			c.verbose(fmt.Sprintf("[DRY-RUN] 将清理: %s", filePath))
		} else {
			// 创建备份
			backupPath, err := c.BackupManager.CreateFileBackup(filePath, fmt.Sprintf("xml_%s", filepath.Base(filePath)))
			if err != nil {
				c.warn(fmt.Sprintf("XML文件备份失败 %s: %v", filePath, err))
			} else {
				c.verbose(fmt.Sprintf("XML文件已备份: %s", backupPath))
				c.Stats.BackupsCreated++
			}

			// 解锁文件
			unlockFile(filePath)

			if err := os.WriteFile(filePath, []byte(modifiedContent), 0644); err != nil {
				return err
			}
			c.verbose(fmt.Sprintf("清理: %s", filePath))
			c.Stats.FilesDeleted++
		}
	}

	return nil
}

// 清理插件目录
func (c *AugmentCleaner) cleanPluginDir(pluginPath string) error {
	if _, err := os.Stat(pluginPath); os.IsNotExist(err) {
		return nil
	}

	// 清理数据目录
	dataDirs := []string{"data", "cache", "logs", "tmp"}
	for _, dir := range dataDirs {
		dirPath := filepath.Join(pluginPath, dir)
		if c.Config.DryRun {
			if _, err := os.Stat(dirPath); err == nil {
				c.verbose(fmt.Sprintf("[DRY-RUN] 将删除目录: %s", dirPath))
			}
		} else {
			if err := os.RemoveAll(dirPath); err == nil {
				c.verbose(fmt.Sprintf("删除目录: %s", dirPath))
				c.Stats.DirsDeleted++
			}
		}
	}

	// 清理特定文件
	return filepath.Walk(pluginPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil
		}

		if info.IsDir() {
			return nil
		}

		fileName := info.Name()
		for _, pattern := range cleanupPatterns {
			if matched, _ := filepath.Match(pattern, fileName); matched {
				if c.Config.DryRun {
					c.verbose(fmt.Sprintf("[DRY-RUN] 将删除: %s", path))
				} else {
					if err := os.Remove(path); err == nil {
						c.verbose(fmt.Sprintf("删除: %s", path))
						c.Stats.FilesDeleted++
					}
				}
				break
			}
		}

		return nil
	})
}

// 清理其他Augment文件
func (c *AugmentCleaner) cleanAugmentFiles(productPath string) error {
	return filepath.Walk(productPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil
		}

		if info.IsDir() {
			return nil
		}

		fileName := strings.ToLower(info.Name())
		if strings.Contains(fileName, "augment") || strings.Contains(fileName, "session") {
			if c.Config.DryRun {
				c.verbose(fmt.Sprintf("[DRY-RUN] 将删除: %s", path))
			} else {
				if err := os.Remove(path); err == nil {
					c.verbose(fmt.Sprintf("删除: %s", path))
					c.Stats.FilesDeleted++
				}
			}
		}

		return nil
	})
}

// 清理项目级数据
func (c *AugmentCleaner) cleanProjectData() error {
	c.log("清理项目级数据...")

	homeDir, _ := os.UserHomeDir()
	projectDirs := []string{
		filepath.Join(homeDir, "Projects"),
		filepath.Join(homeDir, "workspace"),
		filepath.Join(homeDir, "dev"),
		filepath.Join(homeDir, "code"),
		filepath.Join(homeDir, "Documents"),
		filepath.Join(homeDir, "Desktop"),
	}

	for _, projectDir := range projectDirs {
		if _, err := os.Stat(projectDir); os.IsNotExist(err) {
			continue
		}

		err := filepath.Walk(projectDir, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return nil
			}

			if info.IsDir() && info.Name() == ".idea" {
				augmentDir := filepath.Join(path, "augment")
				if c.Config.DryRun {
					if _, err := os.Stat(augmentDir); err == nil {
						c.verbose(fmt.Sprintf("[DRY-RUN] 将删除项目数据: %s", augmentDir))
					}
				} else {
					if err := os.RemoveAll(augmentDir); err == nil {
						c.verbose(fmt.Sprintf("删除项目数据: %s", augmentDir))
						c.Stats.ProjectsCleaned++
					}
				}

				// 清理.idea目录中的Augment文件
				filepath.Walk(path, func(subPath string, subInfo os.FileInfo, subErr error) error {
					if subErr != nil || subInfo.IsDir() {
						return nil
					}

					fileName := strings.ToLower(subInfo.Name())
					if strings.Contains(fileName, "augment") {
						if c.Config.DryRun {
							c.verbose(fmt.Sprintf("[DRY-RUN] 将删除: %s", subPath))
						} else {
							if err := os.Remove(subPath); err == nil {
								c.verbose(fmt.Sprintf("删除: %s", subPath))
								c.Stats.FilesDeleted++
							}
						}
					}
					return nil
				})
			}

			return nil
		})

		if err != nil {
			c.warn(fmt.Sprintf("扫描项目目录 %s 失败: %v", projectDir, err))
		}
	}

	c.success(fmt.Sprintf("项目数据清理完成，共处理 %d 个项目", c.Stats.ProjectsCleaned))
	return nil
}

// 清理缓存文件
func (c *AugmentCleaner) cleanCacheFiles() error {
	c.log("清理缓存文件...")

	cachePaths := []string{c.BasePaths["cache"]}
	if logsPath, exists := c.BasePaths["logs"]; exists {
		cachePaths = append(cachePaths, logsPath)
	}

	for _, cachePath := range cachePaths {
		if _, err := os.Stat(cachePath); os.IsNotExist(err) {
			continue
		}

		err := filepath.Walk(cachePath, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return nil
			}

			fileName := strings.ToLower(info.Name())
			if strings.Contains(fileName, "augment") {
				if c.Config.DryRun {
					c.verbose(fmt.Sprintf("[DRY-RUN] 将删除缓存: %s", path))
				} else {
					if info.IsDir() {
						if err := os.RemoveAll(path); err == nil {
							c.verbose(fmt.Sprintf("删除缓存目录: %s", path))
							c.Stats.DirsDeleted++
						}
					} else {
						if err := os.Remove(path); err == nil {
							c.verbose(fmt.Sprintf("删除缓存文件: %s", path))
							c.Stats.FilesDeleted++
						}
					}
				}
			}

			return nil
		})

		if err != nil {
			c.warn(fmt.Sprintf("清理缓存目录 %s 失败: %v", cachePath, err))
		}

		c.Stats.CachesCleared++
	}

	c.success("缓存文件清理完成")
	return nil
}

// 清理Windows注册表中的JetBrains相关项
func (c *AugmentCleaner) cleanRegistry() error {
	if runtime.GOOS != "windows" {
		c.verbose("跳过注册表清理（非Windows系统）")
		return nil
	}

	c.log("清理Windows注册表中的Augment相关项...")

	registryPaths := []string{
		`HKEY_CURRENT_USER\Software\JetBrains`,
		`HKEY_LOCAL_MACHINE\SOFTWARE\JetBrains`,
	}

	for _, regPath := range registryPaths {
		if err := c.cleanRegistryPath(regPath); err != nil {
			c.warn(fmt.Sprintf("清理注册表路径 %s 失败: %v", regPath, err))
		} else {
			c.Stats.RegistryCleaned++
		}
	}

	c.success("注册表清理完成")
	return nil
}

// 清理指定注册表路径中的Augment相关项
func (c *AugmentCleaner) cleanRegistryPath(regPath string) error {
	// 查询注册表项
	cmd := exec.Command("reg", "query", regPath, "/s")
	output, err := cmd.Output()
	if err != nil {
		// 注册表项不存在或无权限访问，不是错误
		c.verbose(fmt.Sprintf("注册表路径 %s 不存在或无权限访问", regPath))
		return nil
	}

	outputStr := string(output)
	lines := strings.Split(outputStr, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.Contains(strings.ToLower(line), "augment") {
			// 提取注册表键路径
			if strings.HasPrefix(line, "HKEY_") {
				keyPath := strings.Fields(line)[0]
				if c.Config.DryRun {
					c.verbose(fmt.Sprintf("[DRY-RUN] 将删除注册表项: %s", keyPath))
				} else {
					if err := c.deleteRegistryKey(keyPath); err != nil {
						c.verbose(fmt.Sprintf("删除注册表项 %s 失败: %v", keyPath, err))
					} else {
						c.verbose(fmt.Sprintf("删除注册表项: %s", keyPath))
					}
				}
			}
		}
	}

	return nil
}

// 删除注册表键
func (c *AugmentCleaner) deleteRegistryKey(keyPath string) error {
	cmd := exec.Command("reg", "delete", keyPath, "/f")
	return cmd.Run()
}

// 清理 %APPDATA% 下的 .jetbrains 目录
func (c *AugmentCleaner) cleanJetbrainsAppData() error {
	c.log("清理 .jetbrains 目录...")

	var jetbrainsPath string
	homeDir, _ := os.UserHomeDir()

	switch runtime.GOOS {
	case "windows":
		appData := os.Getenv("APPDATA")
		if appData == "" {
			appData = filepath.Join(homeDir, "AppData", "Roaming")
		}
		jetbrainsPath = filepath.Join(appData, ".jetbrains")
	case "darwin":
		jetbrainsPath = filepath.Join(homeDir, ".jetbrains")
	default: // Linux
		jetbrainsPath = filepath.Join(homeDir, ".jetbrains")
	}

	if _, err := os.Stat(jetbrainsPath); os.IsNotExist(err) {
		c.verbose(fmt.Sprintf(".jetbrains 目录不存在: %s", jetbrainsPath))
		return nil
	}

	if c.Config.DryRun {
		c.verbose(fmt.Sprintf("[DRY-RUN] 将删除目录: %s", jetbrainsPath))
	} else {
		if err := os.RemoveAll(jetbrainsPath); err != nil {
			return fmt.Errorf("删除 .jetbrains 目录失败: %v", err)
		}
		c.verbose(fmt.Sprintf("删除目录: %s", jetbrainsPath))
		c.Stats.DirsDeleted++
	}

	c.Stats.AppDataCleaned++
	c.success(".jetbrains 目录清理完成")
	return nil
}

// 清理聊天记录数据 - 集成聊天清理功能
func (c *AugmentCleaner) cleanChatData() error {
	c.log("清理 Augment 聊天记录数据...")

	// 创建聊天清理器
	chatCleaner, err := NewChatCleaner(c)
	if err != nil {
		return fmt.Errorf("创建聊天清理器失败: %v", err)
	}

	// 扫描聊天项目
	projects, err := chatCleaner.ScanChatProjects()
	if err != nil {
		return fmt.Errorf("扫描聊天项目失败: %v", err)
	}

	if len(projects) == 0 {
		c.verbose("未发现聊天记录数据")
		return nil
	}

	// 显示发现的项目信息
	c.log(fmt.Sprintf("发现 %d 个包含聊天数据的项目", len(projects)))
	for _, project := range projects {
		c.verbose(fmt.Sprintf("项目 %s: 聊天文件 %d, 图片文件 %d, 记忆文件 %d, 总大小 %d bytes",
			project.ProjectHash[:8]+"...", len(project.ChatFiles), len(project.ImageFiles),
			len(project.MemoryFiles), project.TotalSize))
	}

	// 配置聊天清理选项
	options := ChatCleanOptions{
		BackupBeforeClean: !c.Config.DryRun, // 非干运行模式时备份
		CleanChatHistory:  true,             // 清理聊天历史
		CleanImages:       true,             // 清理图片资源
		CleanMemories:     false,            // 保留记忆文件（可配置）
		DryRun:            c.Config.DryRun,  // 使用主配置的干运行设置
		TargetProjects:    []string{},       // 清理所有项目
		OlderThan:         nil,              // 清理所有文件（可配置）
	}

	// 如果只清理聊天记录，则只清理聊天文件
	if c.Config.CleanChatOnly {
		options.CleanImages = false
		options.CleanMemories = false
		c.log("仅清理聊天历史文件")
	}

	// 执行聊天数据清理
	if err := chatCleaner.CleanAllChatProjects(options); err != nil {
		return fmt.Errorf("聊天数据清理失败: %v", err)
	}

	c.success("聊天记录数据清理完成")
	return nil
}

// 清理编辑器聊天记录数据 - Cursor 和 VSCode
func (c *AugmentCleaner) cleanEditorChatData() error {
	c.log("清理编辑器聊天记录数据 (Cursor & VSCode)...")

	// 获取用户主目录
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return fmt.Errorf("获取用户主目录失败: %v", err)
	}

	// 定义编辑器配置路径
	var editorPaths []struct {
		name string
		path string
	}

	switch runtime.GOOS {
	case "windows":
		// Windows 路径
		appDataRoaming := os.Getenv("APPDATA")
		if appDataRoaming == "" {
			appDataRoaming = filepath.Join(homeDir, "AppData", "Roaming")
		}
		editorPaths = []struct {
			name string
			path string
		}{
			{"Cursor", filepath.Join(appDataRoaming, "Cursor", "User", "workspaceStorage")},
			{"VSCode", filepath.Join(appDataRoaming, "Code", "User", "workspaceStorage")},
		}
	case "darwin":
		// macOS 路径
		editorPaths = []struct {
			name string
			path string
		}{
			{"Cursor", filepath.Join(homeDir, "Library", "Application Support", "Cursor", "User", "workspaceStorage")},
			{"VSCode", filepath.Join(homeDir, "Library", "Application Support", "Code", "User", "workspaceStorage")},
		}
	default:
		c.verbose("跳过编辑器聊天记录清理（不支持的操作系统）")
		return nil
	}

	totalCleaned := 0
	for _, editor := range editorPaths {
		cleaned, err := c.cleanEditorWorkspaceStorage(editor.name, editor.path)
		if err != nil {
			c.warn(fmt.Sprintf("%s 聊天记录清理失败: %v", editor.name, err))
			continue
		}
		totalCleaned += cleaned
	}

	// 更新统计信息
	c.Stats.EditorWorkspacesCleaned = totalCleaned

	// 新增：处理VSCode配置文件
	c.log("处理VSCode配置文件...")
	vscodeConfigCleaned := 0
	for _, editor := range editorPaths {
		// 处理storage.json
		storageFile := filepath.Join(filepath.Dir(editor.path), "storage.json")
		if _, err := os.Stat(storageFile); err == nil {
			if err := c.processVSCodeJSON(storageFile); err != nil {
				c.warn(fmt.Sprintf("VSCode配置处理失败 %s: %v", storageFile, err))
			} else {
				vscodeConfigCleaned++
			}
		}

		// 处理state.vscdb
		dbFile := filepath.Join(filepath.Dir(editor.path), "state.vscdb")
		if _, err := os.Stat(dbFile); err == nil {
			if err := c.processVSCodeDatabase(dbFile); err != nil {
				c.warn(fmt.Sprintf("VSCode数据库处理失败 %s: %v", dbFile, err))
			} else {
				vscodeConfigCleaned++
			}
		}
	}

	if totalCleaned > 0 || vscodeConfigCleaned > 0 {
		c.success(fmt.Sprintf("编辑器数据清理完成，共清理 %d 个工作区，%d 个配置文件", totalCleaned, vscodeConfigCleaned))
	} else {
		c.log("未发现需要清理的编辑器数据")
	}

	return nil
}

// 清理单个编辑器的工作区存储
func (c *AugmentCleaner) cleanEditorWorkspaceStorage(editorName, workspaceStoragePath string) (int, error) {
	c.verbose(fmt.Sprintf("扫描 %s 工作区存储: %s", editorName, workspaceStoragePath))

	// 检查工作区存储目录是否存在
	if _, err := os.Stat(workspaceStoragePath); os.IsNotExist(err) {
		c.verbose(fmt.Sprintf("%s 工作区存储目录不存在", editorName))
		return 0, nil
	}

	// 扫描所有工作区目录
	workspaceDirs, err := os.ReadDir(workspaceStoragePath)
	if err != nil {
		return 0, fmt.Errorf("读取工作区存储目录失败: %v", err)
	}

	cleanedCount := 0
	for _, workspaceDir := range workspaceDirs {
		if !workspaceDir.IsDir() {
			continue
		}

		workspacePath := filepath.Join(workspaceStoragePath, workspaceDir.Name())
		cleaned, err := c.cleanSingleWorkspace(editorName, workspacePath, workspaceDir.Name())
		if err != nil {
			c.warn(fmt.Sprintf("清理工作区 %s 失败: %v", workspaceDir.Name(), err))
			continue
		}
		if cleaned {
			cleanedCount++
		}
	}

	return cleanedCount, nil
}

// 清理单个工作区的 Augment 相关文件
func (c *AugmentCleaner) cleanSingleWorkspace(editorName, workspacePath, workspaceHash string) (bool, error) {
	c.verbose(fmt.Sprintf("检查工作区: %s (%s)", workspaceHash, editorName))

	augmentDir := filepath.Join(workspacePath, "Augment.vscode-augment")
	stateFile := filepath.Join(workspacePath, "state.vscdb")

	hasAugmentData := false

	// 检查 Augment.vscode-augment 目录
	if _, err := os.Stat(augmentDir); err == nil {
		hasAugmentData = true
		c.verbose(fmt.Sprintf("发现 Augment 聊天目录: %s", augmentDir))

		// 备份并删除 Augment 目录
		if err := c.backupAndRemoveDirectory(augmentDir); err != nil {
			return false, fmt.Errorf("清理 Augment 目录失败: %v", err)
		}
		c.Stats.FilesDeleted++
	}

	// 检查 state.vscdb 文件
	if _, err := os.Stat(stateFile); err == nil {
		hasAugmentData = true
		c.verbose(fmt.Sprintf("发现状态文件: %s", stateFile))

		// 备份并删除状态文件
		if err := c.backupAndRemoveFile(stateFile); err != nil {
			return false, fmt.Errorf("清理状态文件失败: %v", err)
		}
		c.Stats.FilesDeleted++
	}

	if hasAugmentData {
		c.log(fmt.Sprintf("✓ 已清理 %s 工作区: %s", editorName, workspaceHash))
	}

	return hasAugmentData, nil
}

// 备份并删除目录
func (c *AugmentCleaner) backupAndRemoveDirectory(dirPath string) error {
	if c.Config.DryRun {
		c.verbose(fmt.Sprintf("[试运行] 将删除目录: %s", dirPath))
		return nil
	}

	// 创建备份
	backupPath := dirPath + ".bak"
	if err := c.copyDirectory(dirPath, backupPath); err != nil {
		return fmt.Errorf("备份目录失败: %v", err)
	}
	c.verbose(fmt.Sprintf("已备份目录: %s -> %s", dirPath, backupPath))

	// 删除原目录
	if err := os.RemoveAll(dirPath); err != nil {
		return fmt.Errorf("删除目录失败: %v", err)
	}
	c.verbose(fmt.Sprintf("已删除目录: %s", dirPath))

	return nil
}

// 备份并删除文件
func (c *AugmentCleaner) backupAndRemoveFile(filePath string) error {
	if c.Config.DryRun {
		c.verbose(fmt.Sprintf("[试运行] 将删除文件: %s", filePath))
		return nil
	}

	// 创建备份
	backupPath := filePath + ".bak"
	if err := c.copyFile(filePath, backupPath); err != nil {
		return fmt.Errorf("备份文件失败: %v", err)
	}
	c.verbose(fmt.Sprintf("已备份文件: %s -> %s", filePath, backupPath))

	// 删除原文件
	if err := os.Remove(filePath); err != nil {
		return fmt.Errorf("删除文件失败: %v", err)
	}
	c.verbose(fmt.Sprintf("已删除文件: %s", filePath))

	return nil
}

// 复制目录
func (c *AugmentCleaner) copyDirectory(src, dst string) error {
	return filepath.Walk(src, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 计算相对路径
		relPath, err := filepath.Rel(src, path)
		if err != nil {
			return err
		}
		dstPath := filepath.Join(dst, relPath)

		if info.IsDir() {
			return os.MkdirAll(dstPath, info.Mode())
		}

		return c.copyFile(path, dstPath)
	})
}

// 复制文件
func (c *AugmentCleaner) copyFile(src, dst string) error {
	// 确保目标目录存在
	if err := os.MkdirAll(filepath.Dir(dst), 0755); err != nil {
		return err
	}

	srcFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer srcFile.Close()

	dstFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer dstFile.Close()

	_, err = io.Copy(dstFile, srcFile)
	return err
}

// 清理用户目录下的 .augmentcode 目录
func (c *AugmentCleaner) cleanAugmentCodeDir() error {
	c.log("清理 .augmentcode 目录...")

	homeDir, _ := os.UserHomeDir()
	augmentCodePath := filepath.Join(homeDir, ".augmentcode")

	if _, err := os.Stat(augmentCodePath); os.IsNotExist(err) {
		c.verbose(fmt.Sprintf(".augmentcode 目录不存在: %s", augmentCodePath))
		return nil
	}

	if c.Config.DryRun {
		c.verbose(fmt.Sprintf("[DRY-RUN] 将删除目录: %s", augmentCodePath))
		// 显示目录内容
		filepath.Walk(augmentCodePath, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return nil
			}
			c.verbose(fmt.Sprintf("[DRY-RUN] 将删除: %s", path))
			return nil
		})
	} else {
		if err := os.RemoveAll(augmentCodePath); err != nil {
			return fmt.Errorf("删除 .augmentcode 目录失败: %v", err)
		}
		c.verbose(fmt.Sprintf("删除目录: %s", augmentCodePath))
		c.Stats.DirsDeleted++
	}

	c.Stats.AugmentDirCleaned++
	c.success(".augmentcode 目录清理完成")
	return nil
}

// 验证清理结果
func (c *AugmentCleaner) verifyCleanup() error {
	c.log("验证清理结果...")

	remainingFiles := 0

	for _, basePath := range c.BasePaths {
		if _, err := os.Stat(basePath); os.IsNotExist(err) {
			continue
		}

		filepath.Walk(basePath, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return nil
			}

			fileName := strings.ToLower(info.Name())
			if strings.Contains(fileName, "augment") && !info.IsDir() {
				remainingFiles++
				c.verbose(fmt.Sprintf("残留文件: %s", path))
			}

			return nil
		})
	}

	if remainingFiles == 0 {
		c.success("清理验证通过！未发现残留文件")
	} else {
		c.warn(fmt.Sprintf("仍有 %d 个相关文件未清理", remainingFiles))
	}

	return nil
}

// 生成清理报告
func (c *AugmentCleaner) generateReport() error {
	c.Stats.EndTime = time.Now()
	duration := c.Stats.EndTime.Sub(c.Stats.StartTime)

	homeDir, _ := os.UserHomeDir()
	timestamp := time.Now().Format("20060102_150405")
	reportFile := filepath.Join(homeDir, fmt.Sprintf(".augment_cleaner_report_%s.json", timestamp))

	report := map[string]interface{}{
		"timestamp":      timestamp,
		"duration":       duration.String(),
		"config":         c.Config,
		"stats":          c.Stats,
		"os":             runtime.GOOS,
		"products_found": c.discoverInstalledProducts(),
	}

	data, err := json.MarshalIndent(report, "", "  ")
	if err != nil {
		return err
	}

	if err := os.WriteFile(reportFile, data, 0644); err != nil {
		return err
	}

	c.success(fmt.Sprintf("清理报告已生成: %s", reportFile))
	return nil
}

// 主清理函数
func (c *AugmentCleaner) Clean() error {
	c.log("开始Augment插件清理...")

	// 检查IDE运行状态
	if err := c.checkIDERunning(); err != nil {
		return err
	}

	// 检查Augment账号登录状态
	if err := c.checkAugmentLogout(); err != nil {
		return err
	}

	// 执行清理
	if err := c.cleanConfigFiles(); err != nil {
		return fmt.Errorf("配置文件清理失败: %v", err)
	}

	if err := c.cleanCacheFiles(); err != nil {
		c.warn(fmt.Sprintf("缓存清理失败: %v", err))
	}

	if err := c.cleanProjectData(); err != nil {
		c.warn(fmt.Sprintf("项目数据清理失败: %v", err))
	}

	// 新增清理步骤
	if err := c.cleanRegistry(); err != nil {
		c.warn(fmt.Sprintf("注册表清理失败: %v", err))
	}

	if err := c.cleanJetbrainsAppData(); err != nil {
		c.warn(fmt.Sprintf(".jetbrains 目录清理失败: %v", err))
	}

	if err := c.cleanAugmentCodeDir(); err != nil {
		c.warn(fmt.Sprintf(".augmentcode 目录清理失败: %v", err))
	}

	// 新增：清理聊天记录数据
	if err := c.cleanChatData(); err != nil {
		c.warn(fmt.Sprintf("聊天记录清理失败: %v", err))
	}

	// 新增：清理编辑器聊天记录数据
	if err := c.cleanEditorChatData(); err != nil {
		c.warn(fmt.Sprintf("编辑器聊天记录清理失败: %v", err))
	}

	// 验证结果
	if err := c.verifyCleanup(); err != nil {
		c.warn(fmt.Sprintf("清理验证失败: %v", err))
	}

	// 生成报告
	if err := c.generateReport(); err != nil {
		c.warn(fmt.Sprintf("报告生成失败: %v", err))
	}

	c.success("Augment插件清理完成！")

	// 询问是否进行 API 域名测速
	if err := c.optimizeAugmentAPI(); err != nil {
		c.warn(fmt.Sprintf("API域名测速失败: %v", err))
	}

	return nil
}

// 显示帮助信息
func showHelp() {
	fmt.Println("Augment插件清理工具 v4.2")
	fmt.Println("支持全系列JetBrains、Cursor、VSCode产品的Augment插件数据清理和API域名测速")
	fmt.Println()
	fmt.Println("⚠️  重要变更：hosts文件写入功能已禁用，API测速功能保留")
	fmt.Println("   • 程序启动时会自动清理之前的hosts条目")
	fmt.Println("   • API测速功能保留，但不再写入hosts文件")
	fmt.Println()
	fmt.Println("用法:")
	fmt.Println("  augment-cleaner [选项]")
	fmt.Println()
	fmt.Println("清理选项:")
	fmt.Println("  -h, --help          显示帮助信息")
	fmt.Println("  -v, --verbose       详细输出模式")
	fmt.Println("  -d, --dry-run       干运行模式（只显示将要清理的文件）")
	fmt.Println("  -s, --silent        静默模式（无交互）")
	fmt.Println("  --unsafe            非安全模式（更彻底的清理）")
	fmt.Println("  --ide=NAME          只清理指定的IDE（如：IntelliJIdea, IdeaIC）")
	fmt.Println("  --chat-only         只清理聊天记录（包含聊天历史、图片资源等）")
	fmt.Println("  --keep-settings     保留用户设置")
	fmt.Println("  --no-backup         跳过备份创建（更快但有风险）")
	fmt.Println("  --lock-files        清理后锁定文件（防止意外修改）")
	fmt.Println("  --no-database       跳过数据库清理")
	fmt.Println()
	fmt.Println("API测速选项:")
	fmt.Println("  --api-only          只执行API域名测速（不清理，不写入hosts）")
	fmt.Println("  --skip-api          跳过API测速步骤")
	fmt.Println("  --proxy=TYPE:ADDR   指定代理类型和地址")
	fmt.Println("                      TYPE: clash, v2ray, custom")
	fmt.Println("                      ADDR: 代理地址（仅custom类型需要）")
	fmt.Println()
	fmt.Println("支持的IDE:")
	for _, product := range jetbrainsProducts {
		if product.Supported {
			fmt.Printf("  - %s\n", product.DisplayName)
		}
	}
	fmt.Println()
	fmt.Println("示例:")
	fmt.Println("  augment-cleaner                           # 默认清理")
	fmt.Println("  augment-cleaner -d                        # 干运行模式")
	fmt.Println("  augment-cleaner --ide=PyCharm             # 只清理PyCharm")
	fmt.Println("  augment-cleaner --chat-only               # 只清理聊天记录")
	fmt.Println("  augment-cleaner --api-only                # 只执行API测速（不写入hosts）")
	fmt.Println("  augment-cleaner --api-only --proxy=clash  # 使用Clash代理进行API测速")
	fmt.Println("  augment-cleaner --skip-api                # 清理但跳过API测速")
}

// 交互式配置
func (c *AugmentCleaner) interactiveConfig() error {
	if c.Config.SilentMode {
		return nil
	}

	reader := bufio.NewReader(os.Stdin)

	fmt.Println("=== Augment插件清理工具配置 ===")
	fmt.Println()

	// 显示发现的产品
	products := c.discoverInstalledProducts()
	if len(products) > 0 {
		fmt.Println("发现的JetBrains产品:")
		for i, product := range products {
			fmt.Printf("  %d. %s (%s)\n", i+1, product.DisplayName, product.ConfigDir)
		}
		fmt.Println()
	}

	// 询问清理范围
	fmt.Print("选择清理范围 (1=全部, 2=仅聊天记录, 3=指定IDE): ")
	choice, _ := reader.ReadString('\n')
	choice = strings.TrimSpace(choice)

	switch choice {
	case "2":
		c.Config.CleanChatOnly = true
		fmt.Println("✓ 将只清理聊天记录")
	case "3":
		fmt.Print("请输入IDE名称 (如: IntelliJIdea, IdeaIC, PyCharm): ")
		ide, _ := reader.ReadString('\n')
		c.Config.SelectedIDE = strings.TrimSpace(ide)
		fmt.Printf("✓ 将只清理 %s\n", c.Config.SelectedIDE)
	default:
		fmt.Println("✓ 将清理所有JetBrains产品的Augment数据")
	}

	// 询问是否保留设置
	fmt.Print("是否保留用户设置? (y/N): ")
	keep, _ := reader.ReadString('\n')
	keep = strings.TrimSpace(strings.ToLower(keep))
	if keep == "y" || keep == "yes" {
		c.Config.KeepSettings = true
		fmt.Println("✓ 将保留用户设置")
	}

	fmt.Println()
	return nil
}

// CleanWithOptions 带选项的清理函数
func (c *AugmentCleaner) CleanWithOptions(skipAPI bool) error {
	c.log("开始Augment插件清理...")

	// 检查IDE运行状态
	if err := c.checkIDERunning(); err != nil {
		return err
	}

	// 检查Augment账号登录状态
	if err := c.checkAugmentLogout(); err != nil {
		return err
	}

	// 执行清理
	if err := c.cleanConfigFiles(); err != nil {
		return fmt.Errorf("配置文件清理失败: %v", err)
	}

	if err := c.cleanCacheFiles(); err != nil {
		c.warn(fmt.Sprintf("缓存清理失败: %v", err))
	}

	if err := c.cleanProjectData(); err != nil {
		c.warn(fmt.Sprintf("项目数据清理失败: %v", err))
	}

	// 新增清理步骤
	if err := c.cleanRegistry(); err != nil {
		c.warn(fmt.Sprintf("注册表清理失败: %v", err))
	}

	if err := c.cleanJetbrainsAppData(); err != nil {
		c.warn(fmt.Sprintf(".jetbrains 目录清理失败: %v", err))
	}

	if err := c.cleanAugmentCodeDir(); err != nil {
		c.warn(fmt.Sprintf(".augmentcode 目录清理失败: %v", err))
	}

	// 新增：清理聊天记录数据
	if err := c.cleanChatData(); err != nil {
		c.warn(fmt.Sprintf("聊天记录清理失败: %v", err))
	}

	// 新增：清理编辑器聊天记录数据
	if err := c.cleanEditorChatData(); err != nil {
		c.warn(fmt.Sprintf("编辑器聊天记录清理失败: %v", err))
	}

	// 验证结果
	if err := c.verifyCleanup(); err != nil {
		c.warn(fmt.Sprintf("清理验证失败: %v", err))
	}

	// 生成报告
	if err := c.generateReport(); err != nil {
		c.warn(fmt.Sprintf("报告生成失败: %v", err))
	}

	c.success("Augment插件清理完成！")

	// 根据skipAPI参数决定是否执行API测速
	if !skipAPI {
		if err := c.optimizeAugmentAPI(); err != nil {
			c.warn(fmt.Sprintf("API域名测速失败: %v", err))
		}
	} else {
		c.log("跳过API域名测速步骤")
	}

	return nil
}

// runAPIOptimizationOnly 独立运行API测速功能（已禁用hosts写入）
func runAPIOptimizationOnly(dryRun, verbose, silent bool, proxyConfig string) {
	// 添加错误处理
	defer func() {
		if r := recover(); r != nil {
			LogError(fmt.Sprintf("API测速过程中发生panic: %v", r))
			fmt.Printf("❌ API测速过程中发生严重错误: %v\n", r)
			if globalLogger != nil {
				fmt.Printf("📝 详细错误信息已记录到: %s\n", globalLogger.GetLogFilePath())
			}
			// 不使用 os.Exit，让程序正常退出
			fmt.Printf("\n按任意键退出...")
			bufio.NewReader(os.Stdin).ReadBytes('\n')
			return
		}
	}()

	LogInfo("开始独立API域名测速模式")

	fmt.Println()
	fmt.Println("========================================")
	fmt.Println("🚀 独立API域名测速模式")
	fmt.Println("========================================")

	// 创建API测速器
	optimizer := NewAPIOptimizer()
	optimizer.SetConfig(dryRun, verbose, silent)

	LogInfo(fmt.Sprintf("API测速器配置: DryRun=%v, Verbose=%v, Silent=%v", dryRun, verbose, silent))

	// 解析代理配置
	if proxyConfig != "" {
		LogInfo(fmt.Sprintf("解析代理配置: %s", proxyConfig))
		if err := parseProxyConfig(optimizer, proxyConfig); err != nil {
			LogError(fmt.Sprintf("代理配置解析失败: %v", err))
			fmt.Printf("❌ 代理配置解析失败: %v\n", err)
			if globalLogger != nil {
				fmt.Printf("📝 详细错误信息已记录到: %s\n", globalLogger.GetLogFilePath())
			}
			fmt.Printf("\n按任意键退出...")
			bufio.NewReader(os.Stdin).ReadBytes('\n')
			return
		}
	}

	// 运行测速
	LogInfo("开始执行API测速")
	if err := optimizer.RunWithoutPrompt(); err != nil {
		LogError(fmt.Sprintf("API测速失败: %v", err))
		fmt.Printf("❌ API测速失败: %v\n", err)
		if globalLogger != nil {
			fmt.Printf("📝 详细错误信息已记录到: %s\n", globalLogger.GetLogFilePath())
		}

		// 提供用户友好的错误处理
		fmt.Println()
		fmt.Println("💡 可能的解决方案:")
		if strings.Contains(err.Error(), "Access is denied") || strings.Contains(err.Error(), "permission denied") {
			fmt.Println("   • 以管理员身份重新运行程序")
			fmt.Println("   • 检查 hosts 文件是否被其他程序占用")
			fmt.Println("   • 暂时关闭杀毒软件的实时保护")
		}
		fmt.Println("   • 使用 --dry-run 参数预览操作而不实际修改文件")
		fmt.Println("   • 查看日志文件获取详细错误信息")

		fmt.Printf("\n按任意键退出...")
		bufio.NewReader(os.Stdin).ReadBytes('\n')
		return
	}

	LogInfo("API域名测速完成")

	// 显示成功摘要
	showAPISpeedTestSummary(optimizer, dryRun, silent)

	// 退出确认（非静默模式）
	if !silent {
		fmt.Printf("\n✅ 操作完成！按任意键退出...")
		bufio.NewReader(os.Stdin).ReadBytes('\n')
	}
}

// runCleaningOnly 只执行清理功能
func runCleaningOnly() {
	// 添加错误处理
	defer func() {
		if r := recover(); r != nil {
			LogError(fmt.Sprintf("清理过程中发生panic: %v", r))
			fmt.Printf("❌ 清理过程中发生严重错误: %v\n", r)
			if globalLogger != nil {
				fmt.Printf("📝 详细错误信息已记录到: %s\n", globalLogger.GetLogFilePath())
			}
			fmt.Printf("\n按任意键退出...")
			bufio.NewReader(os.Stdin).ReadBytes('\n')
			return
		}
	}()

	LogInfo("开始独立清理模式")

	fmt.Println()
	fmt.Println("========================================")
	fmt.Println("🧹 Augment 插件清理模式")
	fmt.Println("========================================")

	cleaner := NewAugmentCleaner()
	LogInfo("清理器实例创建成功")

	// 交互式配置
	LogInfo("开始交互式配置")
	if err := cleaner.interactiveConfig(); err != nil {
		LogError(fmt.Sprintf("配置失败: %v", err))
		fmt.Printf("❌ 配置失败: %v\n", err)
		if globalLogger != nil {
			fmt.Printf("📝 详细错误信息已记录到: %s\n", globalLogger.GetLogFilePath())
		}
		fmt.Printf("\n按任意键退出...")
		bufio.NewReader(os.Stdin).ReadBytes('\n')
		return
	}

	// 显示配置摘要和确认
	if !cleaner.Config.SilentMode && !cleaner.Config.DryRun {
		fmt.Println()
		fmt.Println("⚠️  警告: 此操作将清理Augment插件的所有跟踪数据")
		fmt.Println("⚠️  包括聊天记录、会话数据、配置文件等")
		fmt.Println()

		reader := bufio.NewReader(os.Stdin)
		fmt.Print("🤔 确认继续清理? (y/N): ")
		confirm, _ := reader.ReadString('\n')
		confirm = strings.TrimSpace(strings.ToLower(confirm))

		if confirm != "y" && confirm != "yes" {
			fmt.Println("❌ 清理已取消")
			return
		}
		fmt.Println()
	}

	// 执行清理（跳过API测速）
	if err := cleaner.CleanWithOptions(true); err != nil {
		LogError(fmt.Sprintf("清理失败: %v", err))
		fmt.Printf("❌ 清理失败: %v\n", err)
		if globalLogger != nil {
			fmt.Printf("📝 详细错误信息已记录到: %s\n", globalLogger.GetLogFilePath())
		}
		fmt.Printf("\n按任意键退出...")
		bufio.NewReader(os.Stdin).ReadBytes('\n')
		return
	}

	// 显示结果摘要
	showCleaningSummary(cleaner)

	// 退出确认（非静默模式）
	if !cleaner.Config.SilentMode {
		fmt.Printf("\n✅ 操作完成！按任意键退出...")
		bufio.NewReader(os.Stdin).ReadBytes('\n')
	}
}

// runFullProcess 执行完整流程（清理 + API测速）
func runFullProcess() {
	// 添加错误处理
	defer func() {
		if r := recover(); r != nil {
			LogError(fmt.Sprintf("完整流程中发生panic: %v", r))
			fmt.Printf("❌ 完整流程中发生严重错误: %v\n", r)
			if globalLogger != nil {
				fmt.Printf("📝 详细错误信息已记录到: %s\n", globalLogger.GetLogFilePath())
			}
			fmt.Printf("\n按任意键退出...")
			bufio.NewReader(os.Stdin).ReadBytes('\n')
			return
		}
	}()

	LogInfo("开始完整流程模式")

	fmt.Println()
	fmt.Println("========================================")
	fmt.Println("🔄 完整流程模式（清理 + API 测速）")
	fmt.Println("========================================")

	cleaner := NewAugmentCleaner()

	// 交互式配置
	if err := cleaner.interactiveConfig(); err != nil {
		LogError(fmt.Sprintf("配置失败: %v", err))
		fmt.Printf("❌ 配置失败: %v\n", err)
		if globalLogger != nil {
			fmt.Printf("📝 详细错误信息已记录到: %s\n", globalLogger.GetLogFilePath())
		}
		fmt.Printf("\n按任意键退出...")
		bufio.NewReader(os.Stdin).ReadBytes('\n')
		return
	}

	// 显示配置摘要和确认
	if !cleaner.Config.SilentMode && !cleaner.Config.DryRun {
		fmt.Println()
		fmt.Println("⚠️  警告: 此操作将清理Augment插件的所有跟踪数据")
		fmt.Println("⚠️  包括聊天记录、会话数据、配置文件等")
		fmt.Println("💡 清理完成后将自动进行API域名测速")
		fmt.Println()

		reader := bufio.NewReader(os.Stdin)
		fmt.Print("🤔 确认继续? (y/N): ")
		confirm, _ := reader.ReadString('\n')
		confirm = strings.TrimSpace(strings.ToLower(confirm))

		if confirm != "y" && confirm != "yes" {
			fmt.Println("❌ 操作已取消")
			return
		}
		fmt.Println()
	}

	// 执行完整流程（包含API测速）
	if err := cleaner.CleanWithOptions(false); err != nil {
		LogError(fmt.Sprintf("操作失败: %v", err))
		fmt.Printf("❌ 操作失败: %v\n", err)
		if globalLogger != nil {
			fmt.Printf("📝 详细错误信息已记录到: %s\n", globalLogger.GetLogFilePath())
		}
		fmt.Printf("\n按任意键退出...")
		bufio.NewReader(os.Stdin).ReadBytes('\n')
		return
	}

	// 显示结果摘要
	showCleaningSummary(cleaner)

	// 退出确认（非静默模式）
	if !cleaner.Config.SilentMode {
		fmt.Printf("\n✅ 操作完成！按任意键退出...")
		bufio.NewReader(os.Stdin).ReadBytes('\n')
	}
}

// showCleaningSummary 显示清理结果摘要
func showCleaningSummary(cleaner *AugmentCleaner) {
	if !cleaner.Config.SilentMode {
		fmt.Println()
		fmt.Println("📊 清理摘要")
		fmt.Println("═══════════════════════════════════════")
		fmt.Printf("🗑️  删除文件: %d\n", cleaner.Stats.FilesDeleted)
		fmt.Printf("📁 删除目录: %d\n", cleaner.Stats.DirsDeleted)
		fmt.Printf("⚙️  清理配置: %d\n", cleaner.Stats.ConfigsCleaned)
		fmt.Printf("🗂️  清理缓存: %d\n", cleaner.Stats.CachesCleared)
		fmt.Printf("📋 清理项目: %d\n", cleaner.Stats.ProjectsCleaned)
		fmt.Printf("🔧 清理注册表: %d\n", cleaner.Stats.RegistryCleaned)
		fmt.Printf("📦 清理AppData: %d\n", cleaner.Stats.AppDataCleaned)
		fmt.Printf("🏠 清理AugmentCode: %d\n", cleaner.Stats.AugmentDirCleaned)
		fmt.Printf("💬 清理聊天项目: %d\n", cleaner.Stats.ChatProjectsCleaned)
		fmt.Printf("📝 清理聊天文件: %d\n", cleaner.Stats.ChatFilesDeleted)
		fmt.Printf("🖥️  清理编辑器工作区: %d\n", cleaner.Stats.EditorWorkspacesCleaned)

		duration := cleaner.Stats.EndTime.Sub(cleaner.Stats.StartTime)
		fmt.Printf("⏱️  耗时: %s\n", duration.String())

		fmt.Println()
		fmt.Println("🎉 操作完成！")

		if cleaner.Config.DryRun {
			fmt.Println()
			fmt.Println("💡 注意: 这是干运行模式，实际上没有删除任何文件")
		}
	}
}

// showAPISpeedTestSummary 显示API测速成功摘要
func showAPISpeedTestSummary(optimizer *APIOptimizer, dryRun, silent bool) {
	if silent {
		return
	}

	fmt.Println()
	fmt.Println("🎉 API域名测速完成！")
	fmt.Println("═══════════════════════════════════════════════════════════════")

	// 获取最快的域名信息
	if len(optimizer.Results) > 0 {
		// 过滤成功的结果并排序
		successResults := make([]DomainSpeedResult, 0)
		for _, result := range optimizer.Results {
			if result.Success && result.IP != "" {
				successResults = append(successResults, result)
			}
		}

		if len(successResults) > 0 {
			// 按延迟排序
			sort.Slice(successResults, func(i, j int) bool {
				return successResults[i].Latency < successResults[j].Latency
			})

			fastest := successResults[0]
			fastestURL, _ := url.Parse(fastest.Domain)
			fastestHost := fastestURL.Host

			fmt.Println("📊 优化结果摘要:")
			fmt.Println("┌─────────────────────────────────────────────────────────────┐")
			fmt.Printf("│ 🥇 最快域名: %-42s │\n", fastestHost)
			fmt.Printf("│ 🌐 最优IP地址: %-40s │\n", fastest.IP)
			fmt.Printf("│ ⚡ 延迟优化: %-44v │\n", fastest.Latency)
			fmt.Printf("│ 📝 成功测试: %-2d 个域名                              │\n", len(successResults))
			fmt.Println("└─────────────────────────────────────────────────────────────┘")

			fmt.Println()
			fmt.Println("⚠️  重要提示:")
			fmt.Println("┌─────────────────────────────────────────────────────────────┐")
			fmt.Printf("│ 🚫 Hosts文件写入功能已禁用                             │\n")
			fmt.Printf("│ 🧹 程序启动时已自动清理之前的hosts条目                 │\n")
			fmt.Printf("│ 💡 测速结果仅供参考，不会修改系统文件                  │\n")
			fmt.Printf("│ 🎯 最快域名: %-38s │\n", fastestHost)
			fmt.Printf("│ 🌐 最优IP: %-43s │\n", fastest.IP)
			fmt.Printf("│ ⚡ 延迟: %-46v │\n", fastest.Latency)
			fmt.Println("└─────────────────────────────────────────────────────────────┘")

			if dryRun {
				fmt.Println()
				fmt.Println("💡 干运行模式:")
				fmt.Println("   • 以上为测速结果预览")
				fmt.Println("   • 移除 --dry-run 参数可执行实际测速")
			}

			fmt.Println()
			fmt.Println("� 如需手动优化网络:")
			fmt.Println("   • 可参考测速结果手动配置网络设置")
			fmt.Println("   • 或使用其他网络优化工具")
			fmt.Println("   • 程序已确保清理了之前的hosts映射")
		}
	}

	fmt.Println()
}

// parseProxyConfig 解析代理配置字符串
func parseProxyConfig(optimizer *APIOptimizer, proxyConfig string) error {
	parts := strings.Split(proxyConfig, ":")
	if len(parts) < 1 {
		return fmt.Errorf("代理配置格式错误")
	}

	proxyType := parts[0]
	switch proxyType {
	case "clash":
		optimizer.Config.ProxyType = "clash"
	case "v2ray":
		optimizer.Config.ProxyType = "v2ray"
	case "custom":
		if len(parts) < 2 {
			return fmt.Errorf("自定义代理需要指定地址，格式：custom:host:port")
		}
		optimizer.Config.ProxyType = "custom"
		optimizer.Config.ProxyAddr = strings.Join(parts[1:], ":")
	default:
		return fmt.Errorf("不支持的代理类型: %s", proxyType)
	}

	return nil
}

// showMainMenu 显示主菜单并获取用户选择
func showMainMenu() int {
	fmt.Println()
	fmt.Println("🎯 请选择要执行的操作：")
	fmt.Println("┌─────────────────────────────────────────────────────────────┐")
	fmt.Println("│                        主菜单                              │")
	fmt.Println("├─────────────────────────────────────────────────────────────┤")
	fmt.Println("│ 1. 🚀 只执行 API 域名测速（不写入hosts）                   │")
	fmt.Println("│ 2. 🧹 只执行 Augment 插件清理                              │")
	fmt.Println("│ 3. 🔄 执行完整流程（清理 + API 测速）                      │")
	fmt.Println("│ 4. 🚪 退出程序                                             │")
	fmt.Println("└─────────────────────────────────────────────────────────────┘")
	fmt.Println()
	fmt.Println("💡 提示：")
	fmt.Println("   • API 测速：测试 d1-d20.api.augmentcode.com 速度（仅显示结果）")
	fmt.Println("   • 插件清理：清理 JetBrains IDE 中的 Augment 插件数据")
	fmt.Println("   • 完整流程：先清理再测速，推荐选择")
	fmt.Println("   ⚠️  注意：hosts写入功能已禁用，程序启动时会自动清理之前的hosts条目")
	fmt.Println()

	reader := bufio.NewReader(os.Stdin)
	for {
		fmt.Print("🤔 请输入选项 (1-4): ")
		input, _ := reader.ReadString('\n')
		input = strings.TrimSpace(input)

		switch input {
		case "1":
			fmt.Println("✅ 已选择：只执行 API 域名测速（不写入hosts）")
			return 1
		case "2":
			fmt.Println("✅ 已选择：只执行 Augment 插件清理")
			return 2
		case "3":
			fmt.Println("✅ 已选择：执行完整流程（清理 + API 测速）")
			return 3
		case "4":
			fmt.Println("👋 感谢使用，再见！")
			return 4
		default:
			fmt.Printf("❌ 无效选择 '%s'，请输入 1-4 之间的数字\n", input)
		}
	}
}

// hasCommandLineArgs 检查是否有命令行参数（除了程序名）
func hasCommandLineArgs() bool {
	return len(os.Args) > 1
}

func main() {
	// 程序版本
	const version = "v4.2"

	// 全局错误处理 - 捕获panic
	defer func() {
		if r := recover(); r != nil {
			// 记录panic信息到日志
			if globalLogger != nil {
				globalLogger.LogPanicWithStack(r)
				fmt.Printf("\n❌ 程序发生严重错误并崩溃！\n")
				fmt.Printf("📝 错误详情已记录到日志文件: %s\n", globalLogger.GetLogFilePath())
				fmt.Printf("💡 请将日志文件发送给开发者以获得技术支持\n")
				fmt.Printf("🔧 错误信息: %v\n", r)
			} else {
				fmt.Printf("\n❌ 程序发生严重错误: %v\n", r)
				fmt.Printf("💡 无法记录详细日志，请联系开发者\n")
			}

			// 等待用户按键后退出
			fmt.Printf("\n按任意键退出...")
			bufio.NewReader(os.Stdin).ReadBytes('\n')
			os.Exit(1)
		}

		// 正常退出时关闭日志
		CloseGlobalLogger()
	}()

	// 初始化全局日志管理器
	if err := InitGlobalLogger(version); err != nil {
		fmt.Printf("⚠️  警告: 无法初始化日志系统: %v\n", err)
		fmt.Printf("程序将继续运行，但不会记录详细日志\n")
	} else {
		LogInfo("全局日志系统初始化成功")
	}

	// 程序启动时自动清理之前的hosts条目
	if err := cleanupPreviousHostsEntries(); err != nil {
		LogWarn(fmt.Sprintf("清理之前的hosts条目失败: %v", err))
		// 不阻止程序继续运行，只记录警告
	}

	// 显示Logo和提示语
	fmt.Println(`
	    █████╗ ██╗   ██╗ ██████╗ ███╗   ███╗███████╗███╗   ██╗████████╗
	   ██╔══██╗██║   ██║██╔════╝ ████╗ ████║██╔════╝████╗  ██║╚══██╔══╝
	   ███████║██║   ██║██║  ███╗██╔████╔██║█████╗  ██╔██╗ ██║   ██║
	   ██╔══██║██║   ██║██║   ██║██║╚██╔╝██║██╔══╝  ██║╚██╗██║   ██║
	   ██║  ██║╚██████╔╝╚██████╔╝██║ ╚═╝ ██║███████╗██║ ╚████║   ██║
	   ╚═╝  ╚═╝ ╚═════╝  ╚═════╝ ╚═╝     ╚═╝╚══════╝╚═╝  ╚═══╝   ╚═╝

						   Augment IDEA清理工具
						 关注公众号：煎饼果子卷AI
	   `)

	fmt.Printf("Augment插件清理工具 %s\n", version)
	fmt.Println("========================================")

	// 记录程序启动
	LogInfo("程序主界面显示完成")

	// 检查是否有命令行参数
	if !hasCommandLineArgs() {
		// 没有命令行参数，显示主菜单
		choice := showMainMenu()
		switch choice {
		case 1:
			// 只执行API测速
			runAPIOptimizationOnly(false, false, false, "")
			return
		case 2:
			// 只执行清理
			runCleaningOnly()
			return
		case 3:
			// 执行完整流程
			runFullProcess()
			return
		case 4:
			// 退出程序
			return
		}
	}

	// 解析命令行参数
	var apiOnly, skipAPI bool
	var proxyConfig string

	cleaner := NewAugmentCleaner()

	for i, arg := range os.Args[1:] {
		switch arg {
		case "-h", "--help":
			showHelp()
			return
		case "-v", "--verbose":
			cleaner.Config.Verbose = true
		case "-d", "--dry-run":
			cleaner.Config.DryRun = true
		case "-s", "--silent":
			cleaner.Config.SilentMode = true
		case "--unsafe":
			cleaner.Config.SafeMode = false
		case "--chat-only":
			cleaner.Config.CleanChatOnly = true
		case "--keep-settings":
			cleaner.Config.KeepSettings = true
		case "--no-backup":
			cleaner.Config.CreateBackups = false
		case "--lock-files":
			cleaner.Config.LockFiles = true
		case "--no-database":
			cleaner.Config.CleanDatabases = false
		case "--api-only":
			apiOnly = true
		case "--skip-api":
			skipAPI = true
		default:
			if strings.HasPrefix(arg, "--ide=") {
				cleaner.Config.SelectedIDE = strings.TrimPrefix(arg, "--ide=")
			} else if strings.HasPrefix(arg, "--proxy=") {
				proxyConfig = strings.TrimPrefix(arg, "--proxy=")
			}
		}
		_ = i
	}

	// 如果只执行API测速
	if apiOnly {
		runAPIOptimizationOnly(cleaner.Config.DryRun, cleaner.Config.Verbose, cleaner.Config.SilentMode, proxyConfig)
		return
	}

	// 显示标题
	if !cleaner.Config.SilentMode {
		fmt.Println("========================================")
		fmt.Println("    Augment插件清理工具 v4.2")
		fmt.Println("  支持全系列JetBrains、Cursor、VSCode产品")
		fmt.Println("========================================")
		fmt.Println()
	}

	// 交互式配置
	if err := cleaner.interactiveConfig(); err != nil {
		cleaner.error(fmt.Sprintf("配置失败: %v", err))
		fmt.Printf("\n按任意键退出...")
		bufio.NewReader(os.Stdin).ReadBytes('\n')
		return
	}

	// 显示配置摘要
	if !cleaner.Config.SilentMode && !cleaner.Config.DryRun {
		fmt.Println("⚠️  警告: 此操作将清理Augment插件的所有跟踪数据")
		fmt.Println("⚠️  包括聊天记录、会话数据、配置文件等")
		fmt.Println()

		reader := bufio.NewReader(os.Stdin)
		fmt.Print("确认继续? (y/N): ")
		confirm, _ := reader.ReadString('\n')
		confirm = strings.TrimSpace(strings.ToLower(confirm))

		if confirm != "y" && confirm != "yes" {
			fmt.Println("清理已取消")
			return
		}
		fmt.Println()
	}

	// 执行清理
	if err := cleaner.CleanWithOptions(skipAPI); err != nil {
		cleaner.error(fmt.Sprintf("清理失败: %v", err))
		fmt.Printf("\n按任意键退出...")
		bufio.NewReader(os.Stdin).ReadBytes('\n')
		return
	}

	// 显示结果摘要
	if !cleaner.Config.SilentMode {
		fmt.Println()
		fmt.Println("=== 清理摘要 ===")
		fmt.Printf("删除文件: %d\n", cleaner.Stats.FilesDeleted)
		fmt.Printf("删除目录: %d\n", cleaner.Stats.DirsDeleted)
		fmt.Printf("清理配置: %d\n", cleaner.Stats.ConfigsCleaned)
		fmt.Printf("清理缓存: %d\n", cleaner.Stats.CachesCleared)
		fmt.Printf("清理项目: %d\n", cleaner.Stats.ProjectsCleaned)
		fmt.Printf("清理注册表: %d\n", cleaner.Stats.RegistryCleaned)
		fmt.Printf("清理AppData: %d\n", cleaner.Stats.AppDataCleaned)
		fmt.Printf("清理AugmentCode: %d\n", cleaner.Stats.AugmentDirCleaned)
		fmt.Printf("清理聊天项目: %d\n", cleaner.Stats.ChatProjectsCleaned)
		fmt.Printf("清理聊天文件: %d\n", cleaner.Stats.ChatFilesDeleted)

		duration := cleaner.Stats.EndTime.Sub(cleaner.Stats.StartTime)
		fmt.Printf("耗时: %s\n", duration.String())

		fmt.Println()
		fmt.Println("========================================================================================")
		fmt.Println("██████╗ ██╗   ██╗██████╗ ██████╗ ████████╗ █████╗ ███╗   ██╗████████╗")
		fmt.Println("██╔══██╗██║   ██║██╔══██╗██╔══██╗╚══██╔══╝██╔══██╗████╗  ██║╚══██╔══╝")
		fmt.Println("██║  ██║██║   ██║██████╔╝██████╔╝   ██║   ███████║██╔██╗ ██║   ██║   ")
		fmt.Println("██║  ██║██║   ██║██╔══██╗██╔══██╗   ██║   ██╔══██║██║╚██╗██║   ██║   ")
		fmt.Println("██████╔╝╚██████╔╝██║  ██║██║  ██║   ██║   ██║  ██║██║ ╚████║   ██║   ")
		fmt.Println("╚═════╝  ╚═════╝ ╚═╝  ╚═╝╚═╝  ╚═╝   ╚═╝   ╚═╝  ╚═╝╚═╝  ╚═══╝   ╚═╝   ")
		fmt.Println()
		fmt.Println("                        关注公众号：煎饼果子卷AI")
		fmt.Println("========================================================================================")
		fmt.Println()
		fmt.Println("下一步:")
		fmt.Println("1. 重启JetBrains、Cursor、VSCode")
		fmt.Println("2. Augment插件将以全新状态启动")
		fmt.Println("3. 如有问题，可使用备份文件恢复")

		if cleaner.Config.DryRun {
			fmt.Println()
			fmt.Println("注意: 这是干运行模式，实际上没有删除任何文件")
		}

		// 退出确认（非静默模式）
		fmt.Printf("\n✅ 操作完成！按任意键退出...")
		bufio.NewReader(os.Stdin).ReadBytes('\n')
	}
}

// Run API测速器主运行方法 - 交互式运行模式（已禁用hosts写入）
func (a *APIOptimizer) Run() error {
	if a.Config.SilentMode {
		return nil
	}

	fmt.Println()
	fmt.Println("========================================")
	fmt.Println("🚀 Augment API 域名测速工具")
	fmt.Println("========================================")
	fmt.Println("💡 此工具将测试 d1-d20.api.augmentcode.com 的访问速度")
	fmt.Println("⚠️  注意：hosts文件写入功能已禁用，仅显示测速结果")
	fmt.Println("🧹 程序启动时已自动清理之前的hosts条目")
	fmt.Println()

	reader := bufio.NewReader(os.Stdin)
	fmt.Print("🤔 是否进行 Augment API 域名测速？(y/N): ")
	confirm, _ := reader.ReadString('\n')
	confirm = strings.TrimSpace(strings.ToLower(confirm))

	if confirm != "y" && confirm != "yes" {
		fmt.Println("⏭️  跳过 API 域名测速")
		return nil
	}

	fmt.Println()
	fmt.Println("🔧 开始配置优化参数...")

	// 配置代理
	if err := a.configureProxy(); err != nil {
		return fmt.Errorf("代理配置失败: %v", err)
	}

	fmt.Println()
	fmt.Println("⚡ 开始域名测速，请稍候...")

	// 测试域名速度
	if err := a.testDomainSpeed(); err != nil {
		return fmt.Errorf("域名测速失败: %v", err)
	}

	// 显示测速结果
	a.displaySpeedResults()

	fmt.Println()
	fmt.Println("⚠️  注意：hosts文件写入功能已禁用")
	fmt.Println("� 测速结果仅供参考，不会修改系统hosts文件")
	fmt.Println("🧹 如需清理之前的hosts条目，程序启动时已自动处理")

	fmt.Println()
	fmt.Println("🎉 API 域名测速完成！")
	return nil
}

// RunWithoutPrompt 无提示运行API测速（用于独立模式，已禁用hosts写入）
func (a *APIOptimizer) RunWithoutPrompt() error {
	a.log("🚀 开始 Augment API 域名测速...")
	a.log("💡 将测试 d1-d20.api.augmentcode.com 的访问速度")
	a.log("⚠️  注意：hosts文件写入功能已禁用")

	// 步骤1: 配置代理
	a.log("🔧 [1/3] 配置代理设置...")
	if err := a.configureProxy(); err != nil {
		return fmt.Errorf("代理配置失败: %v", err)
	}

	// 步骤2: 测试域名速度
	a.log("⚡ [2/3] 开始域名测速...")
	if err := a.testDomainSpeed(); err != nil {
		return fmt.Errorf("域名测速失败: %v", err)
	}

	// 步骤3: 显示测速结果
	a.log("📊 [3/3] 分析测速结果...")
	a.displaySpeedResults()

	// 步骤4已移除: hosts文件写入功能已禁用
	a.log("⚠️  注意：hosts文件写入功能已禁用")
	a.log("� 测速结果仅供参考，不会修改系统hosts文件")

	a.success("🎉 API 域名测速完成！")
	return nil
}

// Augment API 域名测速主函数（保持向后兼容，但已禁用hosts写入）
func (c *AugmentCleaner) optimizeAugmentAPI() error {
	// 创建API测速器实例
	optimizer := NewAPIOptimizer()
	optimizer.SetConfig(c.Config.DryRun, c.Config.Verbose, c.Config.SilentMode)

	// 运行测速（不再写入hosts文件）
	return optimizer.Run()
}

// configureProxy 配置代理设置
func (a *APIOptimizer) configureProxy() error {
	// 如果已经通过命令行参数指定了代理，直接使用
	if a.Config.ProxyType != "none" {
		return a.setupProxyFromConfig()
	}

	reader := bufio.NewReader(os.Stdin)

	// 检测网络环境并给出建议
	a.detectAndRecommendNetwork()

	fmt.Println()
	fmt.Println("🌐 选择代理方式：")
	fmt.Println("┌─────────────────────────────────────────────┐")
	fmt.Println("│ 1. 🔥 Clash 代理 (127.0.0.1:7890) [推荐]    │")
	fmt.Println("│ 2. ⚡ V2Ray 代理 (127.0.0.1:1080) [推荐]    │")
	fmt.Println("│ 3. 🔧 自定义代理                            │")
	fmt.Println("│ 4. 🌍 不使用代理                            │")
	fmt.Println("└─────────────────────────────────────────────┘")
	fmt.Print("🤔 请选择 (1-4): ")

	choice, _ := reader.ReadString('\n')
	choice = strings.TrimSpace(choice)

	a.Proxy = &ProxyConfig{}

	switch choice {
	case "1":
		a.Proxy.Type = "http"
		a.Proxy.Address = "127.0.0.1:7890"
		a.Proxy.Enabled = true
		fmt.Println("🔥 已选择 Clash 代理 (127.0.0.1:7890)")
	case "2":
		a.Proxy.Type = "http"
		a.Proxy.Address = "127.0.0.1:1080"
		a.Proxy.Enabled = true
		fmt.Println("⚡ 已选择 V2Ray 代理 (127.0.0.1:1080)")
	case "3":
		fmt.Print("🔧 请输入代理地址 (格式: host:port): ")
		address, _ := reader.ReadString('\n')
		address = strings.TrimSpace(address)
		if address == "" {
			return fmt.Errorf("代理地址不能为空")
		}
		a.Proxy.Type = "http"
		a.Proxy.Address = address
		a.Proxy.Enabled = true
		fmt.Printf("🔧 已设置自定义代理: %s\n", address)
	case "4":
		a.Proxy.Enabled = false
		fmt.Println("🌍 不使用代理 (可能导致测速较慢或失败)")
	default:
		a.Proxy.Enabled = false
		fmt.Println("⚠️  无效选择，将不使用代理")
	}

	// 验证代理可用性
	if a.Proxy.Enabled {
		if err := a.testProxy(); err != nil {
			fmt.Printf("⚠️  代理测试失败: %v\n", err)
			fmt.Print("是否继续使用此代理？(y/N): ")
			confirm, _ := reader.ReadString('\n')
			confirm = strings.TrimSpace(strings.ToLower(confirm))
			if confirm != "y" && confirm != "yes" {
				a.Proxy.Enabled = false
				fmt.Println("已禁用代理")
			}
		} else {
			fmt.Println("✅ 代理测试成功")
		}
	}

	return nil
}

// setupProxyFromConfig 从配置设置代理
func (a *APIOptimizer) setupProxyFromConfig() error {
	a.Proxy = &ProxyConfig{}

	switch a.Config.ProxyType {
	case "clash":
		a.Proxy.Type = "http"
		a.Proxy.Address = "127.0.0.1:7890"
		a.Proxy.Enabled = true
		a.verbose("使用 Clash 代理: 127.0.0.1:7890")
	case "v2ray":
		a.Proxy.Type = "http"
		a.Proxy.Address = "127.0.0.1:1080"
		a.Proxy.Enabled = true
		a.verbose("使用 V2Ray 代理: 127.0.0.1:1080")
	case "custom":
		if a.Config.ProxyAddr == "" {
			return fmt.Errorf("自定义代理地址不能为空")
		}
		a.Proxy.Type = "http"
		a.Proxy.Address = a.Config.ProxyAddr
		a.Proxy.Enabled = true
		a.verbose(fmt.Sprintf("使用自定义代理: %s", a.Config.ProxyAddr))
	default:
		a.Proxy.Enabled = false
		a.verbose("不使用代理")
	}

	// 验证代理可用性
	if a.Proxy.Enabled {
		if err := a.testProxy(); err != nil {
			a.warn(fmt.Sprintf("代理测试失败: %v", err))
			a.Proxy.Enabled = false
		} else {
			a.success("代理测试成功")
		}
	}

	return nil
}

// testProxy 测试代理可用性
func (a *APIOptimizer) testProxy() error {
	if !a.Proxy.Enabled {
		return nil
	}

	// 创建代理客户端
	proxyURL, err := url.Parse(fmt.Sprintf("http://%s", a.Proxy.Address))
	if err != nil {
		return fmt.Errorf("代理地址格式错误: %v", err)
	}

	transport := &http.Transport{
		Proxy: http.ProxyURL(proxyURL),
	}

	client := &http.Client{
		Transport: transport,
		Timeout:   10 * time.Second,
	}

	// 测试访问 Google
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, "GET", "https://www.google.com", nil)
	if err != nil {
		return err
	}

	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("代理连接失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return fmt.Errorf("代理响应异常: %d", resp.StatusCode)
	}

	return nil
}

// prepareHostsForTesting 为测速准备hosts文件（临时清理Augment条目）
func (a *APIOptimizer) prepareHostsForTesting() error {
	hostsPath := a.getHostsPath()

	// 检查hosts文件是否存在Augment条目
	hasAugmentEntries, existingEntries, err := a.checkExistingAugmentEntries(hostsPath)
	if err != nil {
		return fmt.Errorf("检查现有hosts条目失败: %v", err)
	}

	if !hasAugmentEntries {
		a.verbose("✓ hosts文件中未发现现有Augment条目，测速将基于真实DNS解析")
		return nil
	}

	// 发现现有条目，需要临时清理
	fmt.Println()
	fmt.Println("⚠️  检测到hosts文件中存在Augment API条目")
	fmt.Println("📋 现有条目:")
	for _, entry := range existingEntries {
		fmt.Printf("   %s\n", entry)
	}
	fmt.Println()
	fmt.Println("🔧 为确保测速准确性，将临时清理这些条目...")

	if a.Config.DryRun {
		fmt.Println("🔍 [DRY-RUN] 将临时移除上述条目进行测速，测速完成后恢复")
		return nil
	}

	// 创建临时备份
	tempBackupPath := hostsPath + ".augment.temp"
	if err := a.backupHostsFile(hostsPath, tempBackupPath); err != nil {
		return fmt.Errorf("创建临时备份失败: %v", err)
	}

	// 临时清理Augment条目
	if err := a.temporarilyCleanAugmentEntries(hostsPath); err != nil {
		// 恢复备份
		os.Rename(tempBackupPath, hostsPath)
		return fmt.Errorf("临时清理hosts条目失败: %v", err)
	}

	// 刷新DNS缓存
	a.flushDNSCache()

	a.verbose("✓ 已临时清理hosts文件，测速将基于真实DNS解析")
	return nil
}

// checkExistingAugmentEntries 检查hosts文件中是否存在Augment条目
func (a *APIOptimizer) checkExistingAugmentEntries(hostsPath string) (bool, []string, error) {
	content, err := os.ReadFile(hostsPath)
	if err != nil {
		if os.IsNotExist(err) {
			return false, nil, nil // hosts文件不存在，返回无条目
		}
		return false, nil, err
	}

	lines := strings.Split(string(content), "\n")
	var existingEntries []string
	inAugmentSection := false
	hasAugmentEntries := false

	for _, line := range lines {
		line = strings.TrimSpace(line)

		if strings.Contains(line, "#====Augment Start====") {
			inAugmentSection = true
			hasAugmentEntries = true
			continue
		}
		if strings.Contains(line, "#====Augment End====") {
			inAugmentSection = false
			continue
		}

		// 检查Augment标记段内的条目
		if inAugmentSection && line != "" && !strings.HasPrefix(line, "#") {
			existingEntries = append(existingEntries, line)
		}

		// 检查散落的Augment API条目（没有标记的）
		if !inAugmentSection && strings.Contains(line, ".api.augmentcode.com") && !strings.HasPrefix(line, "#") {
			existingEntries = append(existingEntries, line)
			hasAugmentEntries = true
		}
	}

	return hasAugmentEntries, existingEntries, nil
}

// temporarilyCleanAugmentEntries 临时清理hosts文件中的Augment条目
func (a *APIOptimizer) temporarilyCleanAugmentEntries(hostsPath string) error {
	content, err := os.ReadFile(hostsPath)
	if err != nil {
		return err
	}

	lines := strings.Split(string(content), "\n")
	newLines := make([]string, 0)
	inAugmentSection := false

	for _, line := range lines {
		// 移除Augment标记段
		if strings.Contains(line, "#====Augment Start====") {
			inAugmentSection = true
			continue
		}
		if strings.Contains(line, "#====Augment End====") {
			inAugmentSection = false
			continue
		}
		if inAugmentSection {
			continue
		}

		// 移除散落的Augment API条目
		if strings.Contains(line, ".api.augmentcode.com") && !strings.HasPrefix(strings.TrimSpace(line), "#") {
			continue
		}

		newLines = append(newLines, line)
	}

	// 写入清理后的内容
	newContent := strings.Join(newLines, "\n")
	return os.WriteFile(hostsPath, []byte(newContent), 0644)
}

// getHostsPath 获取hosts文件路径
func (a *APIOptimizer) getHostsPath() string {
	switch runtime.GOOS {
	case "windows":
		return `C:\Windows\System32\drivers\etc\hosts`
	default:
		return `/etc/hosts`
	}
}

// flushDNSCache 刷新DNS缓存
func (a *APIOptimizer) flushDNSCache() {
	a.verbose("🔄 刷新DNS缓存...")

	var cmd *exec.Cmd
	switch runtime.GOOS {
	case "windows":
		cmd = exec.Command("ipconfig", "/flushdns")
	case "darwin":
		cmd = exec.Command("sudo", "dscacheutil", "-flushcache")
	case "linux":
		// 尝试多种Linux DNS缓存刷新方法
		if err := exec.Command("sudo", "systemctl", "restart", "systemd-resolved").Run(); err != nil {
			exec.Command("sudo", "service", "nscd", "restart").Run()
		}
		return
	default:
		a.verbose("⚠️  未知操作系统，跳过DNS缓存刷新")
		return
	}

	if err := cmd.Run(); err != nil {
		a.verbose(fmt.Sprintf("⚠️  DNS缓存刷新失败: %v", err))
	} else {
		a.verbose("✓ DNS缓存已刷新")
	}
}

// restoreHostsAfterTesting 测速完成后恢复hosts文件
func (a *APIOptimizer) restoreHostsAfterTesting() error {
	hostsPath := a.getHostsPath()
	tempBackupPath := hostsPath + ".augment.temp"

	// 检查是否存在临时备份文件
	if _, err := os.Stat(tempBackupPath); os.IsNotExist(err) {
		// 没有临时备份，说明没有进行临时清理
		return nil
	}

	if a.Config.DryRun {
		// dry-run模式下只是清理临时文件
		os.Remove(tempBackupPath)
		return nil
	}

	// 恢复临时备份
	if err := os.Rename(tempBackupPath, hostsPath); err != nil {
		return fmt.Errorf("恢复hosts文件失败: %v", err)
	}

	a.verbose("✓ 已恢复hosts文件，测速完成")
	return nil
}

// testDomainSpeed 测试域名速度
func (a *APIOptimizer) testDomainSpeed() error {
	fmt.Println()
	fmt.Println("🔍 开始测试 Augment API 域名速度...")
	fmt.Println("📡 测试范围: d1.api.augmentcode.com ~ d20.api.augmentcode.com")

	// 检查并临时清理hosts文件中的Augment条目以确保测速准确性
	if err := a.prepareHostsForTesting(); err != nil {
		return fmt.Errorf("准备hosts文件失败: %v", err)
	}

	// 生成域名列表
	domains := make([]string, 20)
	for i := 1; i <= 20; i++ {
		domains[i-1] = fmt.Sprintf("https://d%d.api.augmentcode.com/", i)
	}

	// 创建 HTTP 客户端
	var client *http.Client
	if a.Proxy.Enabled {
		proxyURL, err := url.Parse(fmt.Sprintf("http://%s", a.Proxy.Address))
		if err != nil {
			return fmt.Errorf("代理地址格式错误: %v", err)
		}

		transport := &http.Transport{
			Proxy: http.ProxyURL(proxyURL),
		}

		client = &http.Client{
			Transport: transport,
			Timeout:   5 * time.Second,
		}
		fmt.Printf("🌐 使用代理: %s\n", a.Proxy.Address)
	} else {
		client = &http.Client{
			Timeout: 5 * time.Second,
		}
		fmt.Println("🌍 直连模式（不使用代理）")
	}

	fmt.Println()
	fmt.Println("⚡ 并发测速中，每个域名测试3次取平均值...")

	// 并发测试域名
	a.Results = make([]DomainSpeedResult, 0, 20)
	resultChan := make(chan DomainSpeedResult, 20)
	var wg sync.WaitGroup

	// 使用 5 个 goroutine 并行测试
	semaphore := make(chan struct{}, 5)

	for _, domain := range domains {
		wg.Add(1)
		go func(d string) {
			defer wg.Done()
			semaphore <- struct{}{}        // 获取信号量
			defer func() { <-semaphore }() // 释放信号量

			result := a.testSingleDomain(d, client)
			resultChan <- result

			// 显示进度
			if result.Success {
				fmt.Printf("✅ %s: %v\n", result.Domain, result.Latency)
			} else {
				fmt.Printf("❌ %s: %s\n", result.Domain, result.Error)
			}
		}(domain)
	}

	// 等待所有测试完成
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// 收集结果
	for result := range resultChan {
		a.Results = append(a.Results, result)
	}

	// 测速完成后，恢复hosts文件（如果之前进行了临时清理）
	if err := a.restoreHostsAfterTesting(); err != nil {
		a.verbose(fmt.Sprintf("⚠️  恢复hosts文件失败: %v", err))
	}

	return nil
}

// testSingleDomain 测试单个域名
func (a *APIOptimizer) testSingleDomain(domain string, client *http.Client) DomainSpeedResult {
	result := DomainSpeedResult{
		Domain:  domain,
		Success: false,
	}

	// 测试 3 次取平均值
	var totalLatency time.Duration
	successCount := 0

	for i := 0; i < 3; i++ {
		start := time.Now()

		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		req, err := http.NewRequestWithContext(ctx, "GET", domain, nil)
		if err != nil {
			cancel()
			result.Error = fmt.Sprintf("创建请求失败: %v", err)
			continue
		}

		resp, err := client.Do(req)
		cancel()

		if err != nil {
			result.Error = fmt.Sprintf("请求失败: %v", err)
			continue
		}

		resp.Body.Close()
		latency := time.Since(start)
		totalLatency += latency
		successCount++
	}

	if successCount > 0 {
		result.Success = true
		result.Latency = totalLatency / time.Duration(successCount)

		// 解析域名 IP
		if ip, err := a.resolveDomainIP(domain); err == nil {
			result.IP = ip
		} else {
			// IP解析失败时记录详细错误信息
			if a.Config.Verbose {
				fmt.Printf("    ⚠️  IP解析失败: %v\n", err)
			}
			result.Error = fmt.Sprintf("IP解析失败: %v", err)
		}
	}

	return result
}

// IPResolver IP解析器 - 支持多种解析策略以绕过代理环境
type IPResolver struct {
	timeout    time.Duration
	dnsServers []string // 公共DNS服务器列表
	apiServers []string // IP查询API服务器列表
	verbose    bool
}

// newIPResolver 创建新的IP解析器
func newIPResolver(verbose bool) *IPResolver {
	resolver := &IPResolver{
		timeout: 10 * time.Second,
		dnsServers: []string{
			"*******",        // Google DNS
			"*******",        // Cloudflare DNS
			"**************", // OpenDNS
		},
		apiServers: []string{
			"http://ip-api.com/json/",
			"https://ipinfo.io/",
		},
		verbose: verbose,
	}

	// 检测代理环境
	if verbose {
		resolver.detectProxyEnvironment()
	}

	return resolver
}

// detectProxyEnvironment 检测代理环境
func (r *IPResolver) detectProxyEnvironment() {
	fmt.Println("  🔍 检测网络环境...")

	// 检查常见代理端口
	proxyPorts := []string{"7890", "1080", "8080", "3128"}
	proxyDetected := false

	for _, port := range proxyPorts {
		conn, err := net.DialTimeout("tcp", "127.0.0.1:"+port, 1*time.Second)
		if err == nil {
			conn.Close()
			fmt.Printf("    ⚠️  检测到本地代理端口: %s\n", port)
			proxyDetected = true
		}
	}

	// 检查网络接口中的虚拟网卡
	interfaces, err := net.Interfaces()
	if err == nil {
		for _, iface := range interfaces {
			addrs, err := iface.Addrs()
			if err != nil {
				continue
			}
			for _, addr := range addrs {
				if ipnet, ok := addr.(*net.IPNet); ok {
					ip := ipnet.IP.To4()
					if ip != nil {
						// 检查Clash虚拟网段 198.18.x.x
						if ip[0] == 198 && (ip[1] == 18 || ip[1] == 19) {
							fmt.Printf("    ⚠️  检测到Clash虚拟网卡: %s (%s)\n", iface.Name, ip.String())
							proxyDetected = true
						}
					}
				}
			}
		}
	}

	if proxyDetected {
		fmt.Println("    📡 代理环境已检测到，将使用增强IP解析策略")
	} else {
		fmt.Println("    ✓ 未检测到代理环境")
	}
}

// isValidPublicIP 验证IP是否为有效的公网IP地址
func (r *IPResolver) isValidPublicIP(ipStr string) bool {
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return false
	}

	// 转换为IPv4
	ipv4 := ip.To4()
	if ipv4 == nil {
		return false
	}

	// 检查是否为私有IP或特殊IP段
	// 10.0.0.0/8
	if ipv4[0] == 10 {
		return false
	}
	// **********/12
	if ipv4[0] == 172 && ipv4[1] >= 16 && ipv4[1] <= 31 {
		return false
	}
	// ***********/16
	if ipv4[0] == 192 && ipv4[1] == 168 {
		return false
	}
	// *********/8 (localhost)
	if ipv4[0] == 127 {
		return false
	}
	// ***********/16 (link-local)
	if ipv4[0] == 169 && ipv4[1] == 254 {
		return false
	}
	// **********/15 (Clash虚拟网段)
	if ipv4[0] == 198 && (ipv4[1] == 18 || ipv4[1] == 19) {
		return false
	}

	return true
}

// resolveIPWithDirectDNS 使用指定DNS服务器直接查询IP（绕过代理）
func (r *IPResolver) resolveIPWithDirectDNS(hostname string, dnsServer string) (string, error) {
	resolver := &net.Resolver{
		PreferGo: true,
		Dial: func(ctx context.Context, network, address string) (net.Conn, error) {
			d := net.Dialer{Timeout: r.timeout}
			return d.DialContext(ctx, network, dnsServer+":53")
		},
	}

	ctx, cancel := context.WithTimeout(context.Background(), r.timeout)
	defer cancel()

	ips, err := resolver.LookupIPAddr(ctx, hostname)
	if err != nil {
		return "", err
	}

	// 查找第一个有效的IPv4公网地址
	for _, ip := range ips {
		if ipv4 := ip.IP.To4(); ipv4 != nil {
			ipStr := ipv4.String()
			if r.isValidPublicIP(ipStr) {
				if r.verbose {
					fmt.Printf("    ✓ DNS查询成功 [%s]: %s -> %s\n", dnsServer, hostname, ipStr)
				}
				return ipStr, nil
			}
		}
	}

	return "", fmt.Errorf("未找到有效的IPv4公网地址")
}

// resolveIPWithAPI 使用HTTP API查询真实IP地址
func (r *IPResolver) resolveIPWithAPI(hostname string) (string, error) {
	// 使用ip-api.com服务查询
	apiURL := "http://ip-api.com/json/" + hostname

	client := &http.Client{Timeout: r.timeout}
	resp, err := client.Get(apiURL)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return "", fmt.Errorf("API请求失败，状态码: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	// 解析JSON响应
	var result struct {
		Status string `json:"status"`
		Query  string `json:"query"`
	}

	if err := json.Unmarshal(body, &result); err != nil {
		return "", err
	}

	if result.Status != "success" {
		return "", fmt.Errorf("API查询失败")
	}

	if !r.isValidPublicIP(result.Query) {
		return "", fmt.Errorf("API返回的IP不是有效公网地址: %s", result.Query)
	}

	if r.verbose {
		fmt.Printf("    ✓ API查询成功: %s -> %s\n", hostname, result.Query)
	}

	return result.Query, nil
}

// resolveIPWithFallback 使用多种策略解析IP地址
func (r *IPResolver) resolveIPWithFallback(hostname string) (string, error) {
	if r.verbose {
		fmt.Printf("  🔍 正在解析 %s 的真实IP地址...\n", hostname)
	}

	var lastErr error

	// 策略1: 使用直接DNS查询（绕过代理）
	for _, dnsServer := range r.dnsServers {
		if ip, err := r.resolveIPWithDirectDNS(hostname, dnsServer); err == nil {
			if r.verbose {
				fmt.Printf("    ✓ 使用DNS服务器 %s 解析成功\n", dnsServer)
			}
			return ip, nil
		} else {
			lastErr = err
			if r.verbose {
				fmt.Printf("    ✗ DNS服务器 %s 解析失败: %v\n", dnsServer, err)
			}
		}
	}

	// 策略2: 使用HTTP API查询
	if ip, err := r.resolveIPWithAPI(hostname); err == nil {
		if r.verbose {
			fmt.Printf("    ✓ 使用API查询解析成功\n")
		}
		return ip, nil
	} else {
		lastErr = err
		if r.verbose {
			fmt.Printf("    ✗ API查询失败: %v\n", err)
		}
	}

	// 策略3: 使用系统DNS查询（最后备选）
	ips, err := net.LookupIP(hostname)
	if err == nil {
		for _, ip := range ips {
			if ipv4 := ip.To4(); ipv4 != nil {
				ipStr := ipv4.String()
				if r.isValidPublicIP(ipStr) {
					if r.verbose {
						fmt.Printf("    ✓ 系统DNS解析成功（但可能受代理影响）\n")
						fmt.Printf("    ⚠️  警告: 在代理环境下，此IP可能不准确\n")
					}
					return ipStr, nil
				}
			}
		}
	}

	return "", fmt.Errorf("所有IP解析策略都失败，最后错误: %v", lastErr)
}

// resolveDomainIP 解析域名 IP 地址（增强版，支持代理环境）
func (a *APIOptimizer) resolveDomainIP(domain string) (string, error) {
	// 从 URL 中提取主机名
	u, err := url.Parse(domain)
	if err != nil {
		return "", err
	}

	// 创建IP解析器
	resolver := newIPResolver(a.Config.Verbose)

	// 使用增强的IP解析策略
	return resolver.resolveIPWithFallback(u.Host)
}

// displaySpeedResults 显示测速结果
func (a *APIOptimizer) displaySpeedResults() {
	fmt.Println()
	fmt.Println("📊 测速结果汇总")
	fmt.Println("═══════════════════════════════════════════════════════════════")

	// 过滤成功的结果并排序
	successResults := make([]DomainSpeedResult, 0)
	failedCount := 0
	for _, result := range a.Results {
		if result.Success {
			successResults = append(successResults, result)
		} else {
			failedCount++
		}
	}

	if len(successResults) == 0 {
		fmt.Println("❌ 所有域名测试都失败了")
		fmt.Println("💡 建议检查网络连接或尝试使用代理")
		return
	}

	// 按延迟排序
	sort.Slice(successResults, func(i, j int) bool {
		return successResults[i].Latency < successResults[j].Latency
	})

	fmt.Printf("✅ 成功测试: %d 个域名\n", len(successResults))
	if failedCount > 0 {
		fmt.Printf("❌ 失败测试: %d 个域名\n", failedCount)
	}
	fmt.Println()

	fmt.Println("🏆 最快的域名排行榜 (前10名):")
	fmt.Println("┌────┬─────────────────────────────────────┬──────────┬─────────────────┐")
	fmt.Println("│排名│              域名                   │   延迟   │       IP        │")
	fmt.Println("├────┼─────────────────────────────────────┼──────────┼─────────────────┤")

	count := len(successResults)
	if count > 10 {
		count = 10
	}

	for i := 0; i < count; i++ {
		result := successResults[i]
		// 提取域名部分（去掉https://和末尾的/）
		domainName := strings.TrimPrefix(result.Domain, "https://")
		domainName = strings.TrimSuffix(domainName, "/")

		ip := result.IP
		if ip == "" {
			ip = "未解析"
		}

		fmt.Printf("│ %2d │ %-35s │ %8v │ %-15s │\n",
			i+1, domainName, result.Latency, ip)
	}

	fmt.Println("└────┴─────────────────────────────────────┴──────────┴─────────────────┘")

	if len(successResults) > 0 {
		fastest := successResults[0]
		fastestHost := strings.TrimPrefix(strings.TrimSuffix(fastest.Domain, "/"), "https://")
		fmt.Printf("\n🥇 最快域名: %s (延迟: %v, IP: %s)\n",
			fastestHost, fastest.Latency, fastest.IP)

		fmt.Println()
		fmt.Println("🎯 优化策略说明:")
		fmt.Println("┌─────────────────────────────────────────────────────────────┐")
		fmt.Printf("│ 🚀 将选择最快域名: %-36s │\n", fastestHost)
		fmt.Printf("│ 🌐 使用最优IP地址: %-36s │\n", fastest.IP)
		fmt.Printf("│ ⚡ 预期延迟优化: %-38v │\n", fastest.Latency)
		fmt.Println("│ 📝 所有 d1-d20.api.augmentcode.com 将指向此IP地址       │")
		fmt.Println("│ 💡 确保所有请求都路由到延迟最低的服务器             │")
		fmt.Println("└─────────────────────────────────────────────────────────────┘")
	}
}

// updateHostsFile 更新 hosts 文件 (已禁用 - 不再写入hosts文件)
func (a *APIOptimizer) updateHostsFile() error {
	// 过滤成功的结果并排序
	successResults := make([]DomainSpeedResult, 0)
	for _, result := range a.Results {
		if result.Success && result.IP != "" {
			successResults = append(successResults, result)
		}
	}

	if len(successResults) == 0 {
		return fmt.Errorf("没有可用的域名结果")
	}

	// 按延迟排序，找到最快的域名
	sort.Slice(successResults, func(i, j int) bool {
		return successResults[i].Latency < successResults[j].Latency
	})

	// 获取最快的域名和其IP地址
	fastestResult := successResults[0]
	fastestIP := fastestResult.IP

	// 解析最快域名的主机名（用于日志显示）
	fastestURL, _ := url.Parse(fastestResult.Domain)
	fastestHost := fastestURL.Host

	a.verbose(fmt.Sprintf("选择最优域名: %s (IP: %s, 延迟: %v)", fastestHost, fastestIP, fastestResult.Latency))

	// ===== 已禁用hosts文件写入功能 =====
	// 根据用户要求，不再将测速结果写入hosts文件
	// 只保留测速功能，显示结果但不修改系统文件
	a.log("⚠️  注意: hosts文件写入功能已禁用")
	a.log("💡 测速结果仅供参考，不会修改系统hosts文件")

	fmt.Println()
	fmt.Println("🎉 域名测速完成！")
	fmt.Println("┌─────────────────────────────────────────────────────────────┐")
	fmt.Printf("│ 🚀 最快域名: %-42s │\n", fastestHost)
	fmt.Printf("│ 🌐 最优IP: %-43s │\n", fastestIP)
	fmt.Printf("│ ⚡ 延迟: %-46v │\n", fastestResult.Latency)
	fmt.Println("│ ⚠️  注意: hosts文件写入功能已禁用                      │")
	fmt.Println("│ 💡 如需优化，请手动配置网络设置                       │")
	fmt.Println("└─────────────────────────────────────────────────────────────┘")

	return nil

	// ===== 原hosts文件写入逻辑已完全禁用 =====
	// 以下代码已被注释，不再执行hosts文件的读写操作

	/*
		// 获取 hosts 文件路径
		hostsPath := a.getHostsPath()

		// 检查现有的Augment条目
		hasExisting, existingEntries, err := a.checkExistingAugmentEntries(hostsPath)
		if err != nil {
			return fmt.Errorf("检查现有hosts条目失败: %v", err)
		}

		if a.Config.DryRun {
			// DRY-RUN 预览逻辑已禁用
			return nil
		}

		// 所有hosts文件操作已禁用
		// 包括：权限检查、备份、读取、写入等操作
	*/

	// 在DRY-RUN模式下显示预览信息（但不执行任何文件操作）
	if a.Config.DryRun {
		fmt.Println()
		fmt.Println("🔍 [DRY-RUN] 域名测速结果预览")
		fmt.Println("═══════════════════════════════════════════════════════════════")
		fmt.Printf("🚀 最快域名: %s\n", fastestHost)
		fmt.Printf("🌐 最优IP: %s\n", fastestIP)
		fmt.Printf("⚡ 延迟: %v\n", fastestResult.Latency)
		fmt.Println()
		fmt.Println("⚠️  注意: hosts文件写入功能已禁用")
		fmt.Println("� 此为测速结果预览，不会修改任何系统文件")
		return nil
	}

	// 非DRY-RUN模式下，函数已完成，返回成功
	return nil
}

// checkHostsFilePermissions 检查 hosts 文件权限
func (a *APIOptimizer) checkHostsFilePermissions(hostsPath string) error {
	LogInfo(fmt.Sprintf("检查 hosts 文件权限: %s", hostsPath))

	// 检查文件是否存在
	if _, err := os.Stat(hostsPath); err != nil {
		if os.IsNotExist(err) {
			return fmt.Errorf("hosts 文件不存在: %s", hostsPath)
		}
		return fmt.Errorf("无法访问 hosts 文件: %v", err)
	}

	// 尝试读取文件来检查读权限
	if _, err := os.ReadFile(hostsPath); err != nil {
		return fmt.Errorf("无法读取 hosts 文件，可能权限不足: %v", err)
	}

	// 尝试创建临时文件来检查写权限
	tempPath := hostsPath + ".temp.test"
	if err := os.WriteFile(tempPath, []byte("test"), 0644); err != nil {
		LogWarn(fmt.Sprintf("写权限检查失败: %v", err))

		// 提供详细的权限错误信息
		if runtime.GOOS == "windows" {
			return fmt.Errorf("无法写入 hosts 文件目录，需要管理员权限\n\n💡 解决方案:\n   • 右键点击程序，选择\"以管理员身份运行\"\n   • 或在管理员命令提示符中运行程序\n   • 确保杀毒软件没有阻止文件操作")
		} else {
			return fmt.Errorf("无法写入 hosts 文件目录，需要 root 权限\n\n💡 解决方案:\n   • 使用 sudo 运行程序\n   • 检查文件权限: ls -la %s", hostsPath)
		}
	}

	// 清理临时文件
	os.Remove(tempPath)

	LogInfo("hosts 文件权限检查通过")
	return nil
}

// backupHostsFile 备份 hosts 文件
func (a *APIOptimizer) backupHostsFile(hostsPath, backupPath string) error {
	content, err := os.ReadFile(hostsPath)
	if err != nil {
		return err
	}

	return os.WriteFile(backupPath, content, 0644)
}

// detectAndRecommendNetwork 检测网络环境并给出建议
func (a *APIOptimizer) detectAndRecommendNetwork() {
	fmt.Println("🔍 正在检测网络环境...")

	// 测试访问 Google 来判断网络环境
	client := &http.Client{Timeout: 3 * time.Second}
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, "GET", "https://www.google.com", nil)
	if err != nil {
		fmt.Println("⚠️  网络检测失败，无法确定网络环境")
		return
	}

	_, err = client.Do(req)

	if err != nil {
		// 受限网络环境（可能在国内）
		fmt.Println("🌏 检测到受限网络环境（可能在国内）")
		fmt.Println()
		fmt.Println("💡 强烈建议使用代理进行测速，原因：")
		fmt.Println("   ✅ 避免 DNS 污染，获得真实的 IP 地址")
		fmt.Println("   ✅ 绕过网络限制，获得稳定连接")
		fmt.Println("   ✅ 测速结果更接近实际使用效果")
		fmt.Println("   ✅ 优化后的 hosts 文件效果更好")
		fmt.Println()
		fmt.Println("📍 推荐选择：选项 1 (Clash) 或选项 2 (V2Ray)")
	} else {
		// 开放网络环境
		fmt.Println("🌍 检测到开放网络环境")
		fmt.Println()
		fmt.Println("💡 您可以选择：")
		fmt.Println("   🔄 使用代理：获得更准确的海外服务器测速")
		fmt.Println("   🌐 直接连接：测试本地网络的实际表现")
		fmt.Println()
		fmt.Println("📍 建议：如果有代理，推荐使用代理测速")
	}
}

// cleanupPreviousHostsEntries 清理之前写入hosts文件的Augment条目
// 在程序启动时自动执行，确保清理之前的hosts映射
func cleanupPreviousHostsEntries() error {
	LogInfo("开始清理之前的hosts条目...")

	// 获取hosts文件路径
	var hostsPath string
	switch runtime.GOOS {
	case "windows":
		hostsPath = `C:\Windows\System32\drivers\etc\hosts`
	default:
		hostsPath = `/etc/hosts`
	}

	// 检查hosts文件是否存在
	if _, err := os.Stat(hostsPath); os.IsNotExist(err) {
		LogInfo("hosts文件不存在，跳过清理")
		return nil
	}

	// 检查是否存在Augment条目
	hasAugmentEntries, existingEntries, err := checkExistingAugmentEntriesStandalone(hostsPath)
	if err != nil {
		return fmt.Errorf("检查现有hosts条目失败: %v", err)
	}

	if !hasAugmentEntries {
		LogInfo("未发现需要清理的Augment hosts条目")
		return nil
	}

	LogInfo(fmt.Sprintf("发现 %d 个需要清理的Augment hosts条目", len(existingEntries)))
	for _, entry := range existingEntries {
		LogDebug(fmt.Sprintf("待清理条目: %s", entry))
	}

	// 创建备份
	backupPath := hostsPath + ".augment.startup.backup"
	if err := backupHostsFileStandalone(hostsPath, backupPath); err != nil {
		return fmt.Errorf("备份hosts文件失败: %v", err)
	}
	LogInfo(fmt.Sprintf("已备份hosts文件到: %s", backupPath))

	// 清理Augment条目
	if err := cleanAugmentEntriesFromHosts(hostsPath); err != nil {
		// 如果清理失败，尝试恢复备份
		if restoreErr := os.Rename(backupPath, hostsPath); restoreErr != nil {
			LogError(fmt.Sprintf("清理失败且恢复备份也失败: 清理错误=%v, 恢复错误=%v", err, restoreErr))
		}
		return fmt.Errorf("清理hosts条目失败: %v", err)
	}

	// 刷新DNS缓存
	flushDNSCacheStandalone()

	LogInfo("成功清理之前的Augment hosts条目")
	fmt.Println("🧹 已自动清理之前的Augment API hosts映射")

	return nil
}

// checkExistingAugmentEntriesStandalone 独立的检查hosts文件中Augment条目的函数
func checkExistingAugmentEntriesStandalone(hostsPath string) (bool, []string, error) {
	content, err := os.ReadFile(hostsPath)
	if err != nil {
		if os.IsNotExist(err) {
			return false, nil, nil
		}
		return false, nil, err
	}

	lines := strings.Split(string(content), "\n")
	var existingEntries []string
	inAugmentSection := false
	hasAugmentEntries := false

	for _, line := range lines {
		line = strings.TrimSpace(line)

		if strings.Contains(line, "#====Augment Start====") {
			inAugmentSection = true
			hasAugmentEntries = true
			continue
		}
		if strings.Contains(line, "#====Augment End====") {
			inAugmentSection = false
			continue
		}

		// 检查Augment标记段内的条目
		if inAugmentSection && line != "" && !strings.HasPrefix(line, "#") {
			existingEntries = append(existingEntries, line)
		}

		// 检查散落的Augment API条目（没有标记的）
		if !inAugmentSection && strings.Contains(line, ".api.augmentcode.com") && !strings.HasPrefix(line, "#") {
			existingEntries = append(existingEntries, line)
			hasAugmentEntries = true
		}
	}

	return hasAugmentEntries, existingEntries, nil
}

// backupHostsFileStandalone 独立的备份hosts文件函数
func backupHostsFileStandalone(hostsPath, backupPath string) error {
	content, err := os.ReadFile(hostsPath)
	if err != nil {
		return err
	}
	return os.WriteFile(backupPath, content, 0644)
}

// cleanAugmentEntriesFromHosts 从hosts文件中清理所有Augment相关条目
func cleanAugmentEntriesFromHosts(hostsPath string) error {
	content, err := os.ReadFile(hostsPath)
	if err != nil {
		return err
	}

	lines := strings.Split(string(content), "\n")
	newLines := make([]string, 0)
	inAugmentSection := false

	for _, line := range lines {
		// 移除Augment标记段
		if strings.Contains(line, "#====Augment Start====") {
			inAugmentSection = true
			continue
		}
		if strings.Contains(line, "#====Augment End====") {
			inAugmentSection = false
			continue
		}
		if inAugmentSection {
			continue
		}

		// 移除散落的Augment API条目
		if strings.Contains(line, ".api.augmentcode.com") && !strings.HasPrefix(strings.TrimSpace(line), "#") {
			continue
		}

		newLines = append(newLines, line)
	}

	// 写入清理后的内容
	newContent := strings.Join(newLines, "\n")
	return os.WriteFile(hostsPath, []byte(newContent), 0644)
}

// flushDNSCacheStandalone 独立的刷新DNS缓存函数
func flushDNSCacheStandalone() {
	LogDebug("刷新DNS缓存...")

	var cmd *exec.Cmd
	switch runtime.GOOS {
	case "windows":
		cmd = exec.Command("ipconfig", "/flushdns")
	case "darwin":
		cmd = exec.Command("sudo", "dscacheutil", "-flushcache")
	case "linux":
		// 尝试多种Linux DNS缓存刷新方法
		if err := exec.Command("sudo", "systemctl", "restart", "systemd-resolved").Run(); err != nil {
			exec.Command("sudo", "service", "nscd", "restart").Run()
		}
		return
	default:
		LogDebug("未知操作系统，跳过DNS缓存刷新")
		return
	}

	if err := cmd.Run(); err != nil {
		LogDebug(fmt.Sprintf("DNS缓存刷新失败: %v", err))
	} else {
		LogDebug("DNS缓存已刷新")
	}
}

// ============================================================================
// V2集成功能：智能ID生成和备份管理
// ============================================================================

// NewBackupManager 创建新的备份管理器实例
func NewBackupManager() *BackupManager {
	homeDir, _ := os.UserHomeDir()
	backupDir := filepath.Join(homeDir, ".augment_cleaner_backups")

	// 创建备份目录
	os.MkdirAll(backupDir, 0755)

	return &BackupManager{BackupDir: backupDir}
}

// CreateFileBackup 创建文件备份
func (bm *BackupManager) CreateFileBackup(filePath, backupName string) (string, error) {
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return "", fmt.Errorf("file does not exist: %s", filePath)
	}

	timestamp := time.Now().Format("20060102_150405")
	if backupName == "" {
		backupName = filepath.Base(filePath)
	}

	backupPath := filepath.Join(bm.BackupDir, fmt.Sprintf("%s_%s.bak", backupName, timestamp))

	// 复制文件
	input, err := os.ReadFile(filePath)
	if err != nil {
		return "", err
	}

	err = os.WriteFile(backupPath, input, 0644)
	if err != nil {
		return "", err
	}

	LogInfo(fmt.Sprintf("Created backup: %s -> %s", filePath, backupPath))
	return backupPath, nil
}

// RestoreFileBackup 恢复文件备份
func (bm *BackupManager) RestoreFileBackup(backupPath, targetPath string) error {
	if _, err := os.Stat(backupPath); os.IsNotExist(err) {
		return fmt.Errorf("backup file does not exist: %s", backupPath)
	}

	// 读取备份文件
	backupData, err := os.ReadFile(backupPath)
	if err != nil {
		return fmt.Errorf("failed to read backup file: %v", err)
	}

	// 确保目标目录存在
	os.MkdirAll(filepath.Dir(targetPath), 0755)

	// 解锁目标文件（如果已锁定）
	unlockFile(targetPath)

	// 恢复文件
	err = os.WriteFile(targetPath, backupData, 0644)
	if err != nil {
		return fmt.Errorf("failed to restore file: %v", err)
	}

	LogInfo(fmt.Sprintf("Restored file: %s <- %s", targetPath, backupPath))
	return nil
}

// ListBackups 列出备份文件
func (bm *BackupManager) ListBackups() ([]string, error) {
	var backups []string

	files, err := os.ReadDir(bm.BackupDir)
	if err != nil {
		return nil, err
	}

	for _, file := range files {
		if !file.IsDir() && strings.HasSuffix(file.Name(), ".bak") {
			backups = append(backups, filepath.Join(bm.BackupDir, file.Name()))
		}
	}

	return backups, nil
}

// CleanupOldBackups 清理旧备份
func (bm *BackupManager) CleanupOldBackups(maxBackups int) error {
	backups, err := bm.ListBackups()
	if err != nil {
		return err
	}

	if len(backups) <= maxBackups {
		return nil
	}

	// 按修改时间排序，删除最旧的备份
	type backupInfo struct {
		path    string
		modTime time.Time
	}

	var backupInfos []backupInfo
	for _, backup := range backups {
		info, err := os.Stat(backup)
		if err != nil {
			continue
		}
		backupInfos = append(backupInfos, backupInfo{
			path:    backup,
			modTime: info.ModTime(),
		})
	}

	// 简单排序（最新的在前）
	for i := 0; i < len(backupInfos)-1; i++ {
		for j := i + 1; j < len(backupInfos); j++ {
			if backupInfos[i].modTime.Before(backupInfos[j].modTime) {
				backupInfos[i], backupInfos[j] = backupInfos[j], backupInfos[i]
			}
		}
	}

	// 删除多余的备份
	for i := maxBackups; i < len(backupInfos); i++ {
		if err := os.Remove(backupInfos[i].path); err != nil {
			LogError(fmt.Sprintf("Failed to remove old backup %s: %v", backupInfos[i].path, err))
		} else {
			LogInfo(fmt.Sprintf("Removed old backup: %s", backupInfos[i].path))
		}
	}

	return nil
}

// generateUUID 生成新的UUID
func generateUUID() string {
	return strings.ToLower(uuid.New().String())
}

// generateMachineID 生成机器ID（64字符十六进制）
func generateMachineID() string {
	return strings.ToLower(uuid.New().String() + uuid.New().String())[:64]
}

// generateIDForKey 智能ID生成 - 根据键名生成对应格式的ID
func generateIDForKey(key string) string {
	key = strings.ToLower(key)

	// 机器ID相关键值
	if strings.Contains(key, "machineid") || strings.Contains(key, "machine_id") {
		return generateMachineID()
	}

	// 设备ID相关键值
	if strings.Contains(key, "deviceid") || strings.Contains(key, "device_id") {
		return generateUUID()
	}

	// 用户ID相关键值
	if strings.Contains(key, "userid") || strings.Contains(key, "user_id") {
		return generateUUID()
	}

	// 遥测ID相关键值
	if strings.Contains(key, "telemetry") || strings.Contains(key, "sqm") {
		return generateUUID()
	}

	// 默认返回UUID
	return generateUUID()
}

// validateIDFormat 验证ID格式
func validateIDFormat(id, expectedType string) bool {
	if id == "" {
		return false
	}

	switch strings.ToLower(expectedType) {
	case "uuid":
		// UUID格式: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
		if len(id) == 36 && strings.Count(id, "-") == 4 {
			return true
		}
	case "machineid":
		// 机器ID格式: 64字符十六进制
		if len(id) == 64 {
			for _, char := range id {
				if !((char >= '0' && char <= '9') || (char >= 'a' && char <= 'f')) {
					return false
				}
			}
			return true
		}
	}

	return false
}

// lockFile 锁定文件（设置只读）
func lockFile(filePath string) error {
	switch runtime.GOOS {
	case "windows":
		return os.Chmod(filePath, 0444)
	default:
		return os.Chmod(filePath, 0444)
	}
}

// lockFileAdvanced 高级文件锁定（平台特定实现）
func lockFileAdvanced(filePath string) error {
	switch runtime.GOOS {
	case "windows":
		// 使用 attrib +R 命令设置只读属性
		cmd := exec.Command("attrib", "+R", filePath)
		if err := cmd.Run(); err != nil {
			// 如果命令失败，回退到基础权限设置
			return os.Chmod(filePath, 0444)
		}
		return nil
	case "darwin":
		// macOS: 先设置只读权限，再设置不可变标志
		if err := os.Chmod(filePath, 0444); err != nil {
			return err
		}
		// 使用 chflags uchg 设置不可变标志
		cmd := exec.Command("chflags", "uchg", filePath)
		if err := cmd.Run(); err != nil {
			LogWarn(fmt.Sprintf("Warning: Failed to set immutable flag for %s: %v", filePath, err))
		}
		return nil
	default:
		// Linux: 使用 chmod 设置只读权限
		return os.Chmod(filePath, 0444)
	}
}

// unlockFile 解锁文件
func unlockFile(filePath string) error {
	return os.Chmod(filePath, 0644)
}

// unlockFileAdvanced 高级文件解锁（平台特定实现）
func unlockFileAdvanced(filePath string) error {
	switch runtime.GOOS {
	case "windows":
		// 使用 attrib -R 命令移除只读属性
		cmd := exec.Command("attrib", "-R", filePath)
		if err := cmd.Run(); err != nil {
			// 如果命令失败，回退到基础权限设置
			return os.Chmod(filePath, 0644)
		}
		return nil
	case "darwin":
		// macOS: 先移除不可变标志，再设置可写权限
		cmd := exec.Command("chflags", "nouchg", filePath)
		if err := cmd.Run(); err != nil {
			LogWarn(fmt.Sprintf("Warning: Failed to remove immutable flag for %s: %v", filePath, err))
		}
		return os.Chmod(filePath, 0644)
	default:
		// Linux: 使用 chmod 设置可写权限
		return os.Chmod(filePath, 0644)
	}
}

// isFileLocked 检查文件是否被锁定
func isFileLocked(filePath string) bool {
	info, err := os.Stat(filePath)
	if err != nil {
		return false
	}

	// 检查文件权限
	mode := info.Mode()

	// 如果文件没有写权限，认为是锁定状态
	return mode&0200 == 0 // 检查用户写权限位
}

// isSQLiteDatabase 检查是否为SQLite数据库
func isSQLiteDatabase(filePath string) bool {
	file, err := os.Open(filePath)
	if err != nil {
		return false
	}
	defer file.Close()

	header := make([]byte, 16)
	n, err := file.Read(header)
	if err != nil || n < 16 {
		return false
	}

	return string(header[:15]) == "SQLite format 3"
}

// cleanSQLiteDatabase 清理SQLite数据库
func (c *AugmentCleaner) cleanSQLiteDatabase(dbPath string) (int, error) {
	if !isSQLiteDatabase(dbPath) {
		return 0, nil
	}

	db, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		return 0, err
	}
	defer db.Close()

	recordsCleaned := 0

	// 获取所有表名
	rows, err := db.Query("SELECT name FROM sqlite_master WHERE type='table'")
	if err != nil {
		return 0, err
	}
	defer rows.Close()

	var tables []string
	for rows.Next() {
		var tableName string
		if err := rows.Scan(&tableName); err != nil {
			continue
		}
		tables = append(tables, tableName)
	}

	// 清理每个表
	for _, tableName := range tables {
		// 获取表结构
		columnRows, err := db.Query(fmt.Sprintf("PRAGMA table_info(%s)", tableName))
		if err != nil {
			continue
		}

		var textColumns []string
		for columnRows.Next() {
			var cid int
			var name, dataType string
			var notNull, pk int
			var defaultValue interface{}

			if err := columnRows.Scan(&cid, &name, &dataType, &notNull, &defaultValue, &pk); err != nil {
				continue
			}

			dataType = strings.ToUpper(dataType)
			if strings.Contains(dataType, "TEXT") || strings.Contains(dataType, "VARCHAR") || strings.Contains(dataType, "CHAR") {
				textColumns = append(textColumns, name)
			}
		}
		columnRows.Close()

		// 清理匹配的记录
		for _, column := range textColumns {
			for _, pattern := range jetbrainsConfig.AugmentPatterns {
				// 计算要删除的记录数
				countQuery := fmt.Sprintf("SELECT COUNT(*) FROM %s WHERE %s LIKE ?", tableName, column)
				var count int
				if err := db.QueryRow(countQuery, pattern).Scan(&count); err != nil {
					continue
				}

				if count > 0 {
					// 删除记录
					deleteQuery := fmt.Sprintf("DELETE FROM %s WHERE %s LIKE ?", tableName, column)
					if _, err := db.Exec(deleteQuery, pattern); err != nil {
						c.error(fmt.Sprintf("Failed to delete from %s.%s: %v", tableName, column, err))
						continue
					}

					recordsCleaned += count
					c.verbose(fmt.Sprintf("Cleaned %d records from %s.%s matching %s", count, tableName, column, pattern))
				}
			}
		}
	}

	return recordsCleaned, nil
}

// cleanVSCodeDatabase 清理VSCode数据库
func (c *AugmentCleaner) cleanVSCodeDatabase(dbPath string) (int, error) {
	if !isSQLiteDatabase(dbPath) {
		return 0, nil
	}

	db, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		return 0, err
	}
	defer db.Close()

	recordsCleaned := 0

	// VSCode数据库通常有ItemTable表
	patterns := []string{"%augment%", "%Augment%", "%AUGMENT%", "%telemetry%", "%machine%", "%device%"}

	for _, pattern := range patterns {
		// 计算要删除的记录数
		var count int
		countQuery := "SELECT COUNT(*) FROM ItemTable WHERE key LIKE ?"
		if err := db.QueryRow(countQuery, pattern).Scan(&count); err != nil {
			continue
		}

		if count > 0 {
			// 删除记录
			deleteQuery := "DELETE FROM ItemTable WHERE key LIKE ?"
			if _, err := db.Exec(deleteQuery, pattern); err != nil {
				c.error(fmt.Sprintf("Failed to delete records matching %s: %v", pattern, err))
				continue
			}

			recordsCleaned += count
			c.verbose(fmt.Sprintf("Cleaned %d records matching %s", count, pattern))
		}
	}

	return recordsCleaned, nil
}

// findDatabaseFiles 查找数据库文件
func (c *AugmentCleaner) findDatabaseFiles() []string {
	var dbFiles []string

	// 在JetBrains目录中查找数据库文件
	for _, basePath := range c.BasePaths {
		if _, err := os.Stat(basePath); os.IsNotExist(err) {
			continue
		}

		// 遍历所有子目录查找数据库文件
		filepath.WalkDir(basePath, func(path string, d fs.DirEntry, err error) error {
			if err != nil {
				return nil
			}

			if !d.IsDir() {
				ext := strings.ToLower(filepath.Ext(path))
				if ext == ".db" || ext == ".sqlite" || ext == ".sqlite3" {
					dbFiles = append(dbFiles, path)
				}
			}
			return nil
		})
	}

	return dbFiles
}

// processVSCodeJSON 处理VSCode JSON配置文件
func (c *AugmentCleaner) processVSCodeJSON(filePath string) error {
	c.verbose(fmt.Sprintf("处理VSCode JSON文件: %s", filePath))

	// 读取JSON文件
	data, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("failed to read file: %v", err)
	}

	var jsonData map[string]interface{}
	if err := json.Unmarshal(data, &jsonData); err != nil {
		return fmt.Errorf("failed to parse JSON: %v", err)
	}

	// 创建备份
	if c.Config.CreateBackups && !c.Config.DryRun {
		backupPath, err := c.BackupManager.CreateFileBackup(filePath, fmt.Sprintf("vscode_%s", filepath.Base(filePath)))
		if err != nil {
			c.warn(fmt.Sprintf("VSCode JSON备份失败 %s: %v", filePath, err))
		} else {
			c.verbose(fmt.Sprintf("VSCode JSON已备份: %s", backupPath))
			c.Stats.BackupsCreated++
		}
	}

	// 处理遥测键值
	modified := false
	for _, key := range vscodeConfig.TelemetryKeys {
		if oldValue, exists := jsonData[key]; exists {
			oldID := fmt.Sprintf("%v", oldValue)

			// 生成新ID
			var newID string
			if strings.Contains(key, "machineId") {
				newID = generateMachineID()
			} else {
				newID = generateUUID()
			}

			if c.Config.DryRun {
				c.verbose(fmt.Sprintf("[DRY-RUN] 将更新 %s: %s -> %s", key, oldID, newID))
			} else {
				jsonData[key] = newID
				modified = true
				c.verbose(fmt.Sprintf("更新 %s: %s -> %s", key, oldID, newID))
			}
		}
	}

	// 写入修改后的JSON
	if modified && !c.Config.DryRun {
		unlockFile(filePath)

		newData, err := json.MarshalIndent(jsonData, "", "  ")
		if err != nil {
			return fmt.Errorf("failed to marshal JSON: %v", err)
		}

		if err := os.WriteFile(filePath, newData, 0644); err != nil {
			return fmt.Errorf("failed to write file: %v", err)
		}

		if c.Config.LockFiles {
			lockFile(filePath)
			c.Stats.FilesLocked++
		}

		c.Stats.FilesDeleted++
	}

	return nil
}

// processVSCodeDatabase 处理VSCode数据库文件
func (c *AugmentCleaner) processVSCodeDatabase(filePath string) error {
	c.verbose(fmt.Sprintf("处理VSCode数据库: %s", filePath))

	// 创建备份
	if c.Config.CreateBackups && !c.Config.DryRun {
		backupPath, err := c.BackupManager.CreateFileBackup(filePath, fmt.Sprintf("vscode_db_%s", filepath.Base(filePath)))
		if err != nil {
			c.warn(fmt.Sprintf("VSCode数据库备份失败 %s: %v", filePath, err))
		} else {
			c.verbose(fmt.Sprintf("VSCode数据库已备份: %s", backupPath))
			c.Stats.BackupsCreated++
		}
	}

	// 清理数据库中的遥测数据
	if !c.Config.DryRun {
		recordsCleaned, err := c.cleanVSCodeDatabase(filePath)
		if err != nil {
			return fmt.Errorf("database cleaning failed: %v", err)
		}

		if recordsCleaned > 0 {
			c.verbose(fmt.Sprintf("VSCode数据库清理完成: %d 条记录", recordsCleaned))
			c.Stats.RecordsCleaned += recordsCleaned
			c.Stats.DatabasesCleaned++
		}
	} else {
		c.verbose(fmt.Sprintf("[DRY-RUN] 将清理VSCode数据库: %s", filePath))
	}

	return nil
}
