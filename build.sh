#!/bin/bash

# Augment清理工具构建脚本

echo "🧹 Augment清理工具 - 跨平台构建"
echo "=================================="

# 版本管理
if [ ! -f "version.txt" ]; then
    echo "2.0" > version.txt
    echo "📝 创建初始版本文件: 2.0"
fi

# 读取当前版本
CURRENT_VERSION=$(cat version.txt | tr -d '\n\r')
echo "📋 当前版本: $CURRENT_VERSION"

# 计算新版本 (增加0.1)
IFS='.' read -r MAJOR MINOR <<< "$CURRENT_VERSION"
NEW_MINOR=$((MINOR + 1))
NEW_VERSION="$MAJOR.$NEW_MINOR"

echo "🔄 更新版本: $CURRENT_VERSION → $NEW_VERSION"
echo "$NEW_VERSION" > version.txt

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ 未找到Go编译环境"
    exit 1
fi

echo "✅ 检测到Go编译环境: $(go version)"

# 清空并创建构建目录
echo "🗑️  清空构建目录..."
rm -rf dist
mkdir -p dist

echo "📦 开始编译清理工具 v$NEW_VERSION..."

# 构建目标平台
platforms=(
    "windows/amd64"
    "linux/amd64"
    "darwin/amd64"
    "windows/arm64"
    "linux/arm64"
    "darwin/arm64"
    "linux/arm"
)

for platform in "${platforms[@]}"; do
    IFS='/' read -r os arch <<< "$platform"

    echo "构建 $os/$arch..."

    output_name="augment-cleaner-$os-$arch-v$NEW_VERSION"
    if [ "$os" = "windows" ]; then
        output_name="${output_name}.exe"
    fi

    env GOOS=$os GOARCH=$arch go build -o "dist/$output_name" augment_cleaner.go

    if [ $? -eq 0 ]; then
        # 为Unix系统的可执行文件添加执行权限
        if [ "$os" != "windows" ]; then
            chmod +x "dist/$output_name"
        fi
        echo "✅ $output_name"
    else
        echo "❌ 构建失败: $output_name"
    fi
done

echo ""
echo "📁 构建结果:"
ls -la dist/

echo ""
echo "🎉 构建完成！版本: v$NEW_VERSION"
echo ""
echo "使用方法:"
echo ""
echo "AMD64架构 (x86_64):"
echo "  Windows: dist/augment-cleaner-windows-amd64-v$NEW_VERSION.exe"
echo "  Linux:   dist/augment-cleaner-linux-amd64-v$NEW_VERSION"
echo "  macOS:   dist/augment-cleaner-darwin-amd64-v$NEW_VERSION"
echo ""
echo "ARM64架构 (Apple Silicon, ARM服务器):"
echo "  Windows: dist/augment-cleaner-windows-arm64-v$NEW_VERSION.exe"
echo "  Linux:   dist/augment-cleaner-linux-arm64-v$NEW_VERSION"
echo "  macOS:   dist/augment-cleaner-darwin-arm64-v$NEW_VERSION"
echo ""
echo "ARM32架构 (树莓派等嵌入式设备):"
echo "  Linux:   dist/augment-cleaner-linux-arm-v$NEW_VERSION"
echo ""
echo "📝 版本已更新至: v$NEW_VERSION"

