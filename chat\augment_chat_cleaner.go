package chat

import (
	"fmt"
	"io"
	"os"
	"os/user"
	"path/filepath"
	"runtime"
	"strings"
	"time"
)

// ProjectInfo 项目信息结构
type ProjectInfo struct {
	ProjectHash  string
	StorePath    string
	LastModified time.Time
	TotalSize    int64
	ChatFiles    []string
	ImageFiles   []string
	MemoryFiles  []string
}

// CleanOptions 清理选项配置
type CleanOptions struct {
	BackupBeforeClean bool           // 清理前是否备份
	CleanChatHistory  bool           // 是否清理聊天历史
	CleanImages       bool           // 是否清理图片资源
	CleanMemories     bool           // 是否清理记忆文件
	DryRun            bool           // 是否为试运行模式
	TargetProjects    []string       // 指定清理的项目hash列表，空则清理所有
	OlderThan         *time.Duration // 只清理指定时间之前的文件
}

// AugmentCleaner 清理器接口
type AugmentCleaner interface {
	GetAugmentBaseDir() (string, error)
	ScanProjects() ([]ProjectInfo, error)
	CleanProject(projectHash string, options CleanOptions) error
	CleanAll(options CleanOptions) error
	BackupProject(projectHash string, backupDir string) error
}

// augmentCleaner 清理器实现
type augmentCleaner struct {
	baseDir string
	logger  Logger
}

// Logger 日志接口
type Logger interface {
	Info(msg string, args ...interface{})
	Warn(msg string, args ...interface{})
	Error(msg string, args ...interface{})
}

// NewAugmentCleaner 创建新的清理器实例
func NewAugmentCleaner(logger Logger) (AugmentCleaner, error) {
	cleaner := &augmentCleaner{logger: logger}

	baseDir, err := cleaner.GetAugmentBaseDir()
	if err != nil {
		return nil, fmt.Errorf("failed to get augment base directory: %w", err)
	}

	cleaner.baseDir = baseDir
	return cleaner, nil
}

// GetAugmentBaseDir 跨平台获取 Augment 基础目录
func (c *augmentCleaner) GetAugmentBaseDir() (string, error) {
	// 优先检查环境变量 AUGMENT_STATE_HOME
	if augmentHome := os.Getenv("AUGMENT_STATE_HOME"); augmentHome != "" {
		c.logger.Info("Using AUGMENT_STATE_HOME: %s", augmentHome)
		return augmentHome, nil
	}

	// 获取用户主目录
	currentUser, err := user.Current()
	if err != nil {
		return "", fmt.Errorf("failed to get current user: %w", err)
	}

	homeDir := currentUser.HomeDir
	if homeDir == "" {
		return "", fmt.Errorf("user home directory is empty")
	}

	// 跨平台路径构建
	var augmentDir string
	switch runtime.GOOS {
	case "windows":
		// Windows: C:\Users\<USER>\.augmentcode
		augmentDir = filepath.Join(homeDir, ".augmentcode")
	case "darwin", "linux":
		// macOS/Linux: /Users/<USER>/.augmentcode 或 /home/<USER>/.augmentcode
		augmentDir = filepath.Join(homeDir, ".augmentcode")
	default:
		// 其他系统默认处理
		augmentDir = filepath.Join(homeDir, ".augmentcode")
	}

	c.logger.Info("Augment base directory: %s", augmentDir)
	return augmentDir, nil
}

// ScanProjects 扫描所有项目的聊天记录
func (c *augmentCleaner) ScanProjects() ([]ProjectInfo, error) {
	projectsDir := filepath.Join(c.baseDir, "projects")

	// 检查 projects 目录是否存在
	if _, err := os.Stat(projectsDir); os.IsNotExist(err) {
		c.logger.Warn("Projects directory does not exist: %s", projectsDir)
		return []ProjectInfo{}, nil
	}

	// 读取所有项目目录
	entries, err := os.ReadDir(projectsDir)
	if err != nil {
		return nil, fmt.Errorf("failed to read projects directory: %w", err)
	}

	var projects []ProjectInfo
	for _, entry := range entries {
		if !entry.IsDir() {
			continue
		}

		projectHash := entry.Name()
		pluginStoreDir := filepath.Join(projectsDir, projectHash, "plugin-file-store")

		// 检查 plugin-file-store 目录是否存在
		if _, err := os.Stat(pluginStoreDir); os.IsNotExist(err) {
			c.logger.Info("Skipping project %s: no plugin-file-store directory", projectHash)
			continue
		}

		// 扫描项目文件
		projectInfo, err := c.scanProjectFiles(projectHash, pluginStoreDir)
		if err != nil {
			c.logger.Error("Failed to scan project %s: %v", projectHash, err)
			continue
		}

		projects = append(projects, projectInfo)
	}

	c.logger.Info("Found %d projects with chat data", len(projects))
	return projects, nil
}

// scanProjectFiles 扫描单个项目的文件
func (c *augmentCleaner) scanProjectFiles(projectHash, storePath string) (ProjectInfo, error) {
	project := ProjectInfo{
		ProjectHash: projectHash,
		StorePath:   storePath,
		ChatFiles:   []string{},
		ImageFiles:  []string{},
		MemoryFiles: []string{},
	}

	err := filepath.Walk(storePath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.IsDir() {
			return nil
		}

		// 更新最后修改时间和总大小
		if info.ModTime().After(project.LastModified) {
			project.LastModified = info.ModTime()
		}
		project.TotalSize += info.Size()

		// 根据文件名和扩展名分类
		fileName := strings.ToLower(info.Name())
		switch {
		case strings.Contains(fileName, "chat") || strings.Contains(fileName, "history"):
			project.ChatFiles = append(project.ChatFiles, path)
		case strings.Contains(fileName, "memories") || strings.HasSuffix(fileName, ".md"):
			project.MemoryFiles = append(project.MemoryFiles, path)
		case isImageFile(fileName):
			project.ImageFiles = append(project.ImageFiles, path)
		default:
			// 其他文件也归类到聊天文件中
			project.ChatFiles = append(project.ChatFiles, path)
		}

		return nil
	})

	return project, err
}

// isImageFile 判断是否为图片文件
func isImageFile(fileName string) bool {
	imageExts := []string{".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg"}
	for _, ext := range imageExts {
		if strings.HasSuffix(fileName, ext) {
			return true
		}
	}
	return false
}

// CleanProject 清理指定项目的聊天记录
func (c *augmentCleaner) CleanProject(projectHash string, options CleanOptions) error {
	c.logger.Info("Starting cleanup for project: %s", projectHash)

	// 获取项目信息
	projects, err := c.ScanProjects()
	if err != nil {
		return fmt.Errorf("failed to scan projects: %w", err)
	}

	var targetProject *ProjectInfo
	for _, project := range projects {
		if project.ProjectHash == projectHash {
			targetProject = &project
			break
		}
	}

	if targetProject == nil {
		return fmt.Errorf("project not found: %s", projectHash)
	}

	// 备份处理
	if options.BackupBeforeClean {
		backupDir := filepath.Join(c.baseDir, "backups", fmt.Sprintf("%s_%d", projectHash, time.Now().Unix()))
		if err := c.BackupProject(projectHash, backupDir); err != nil {
			return fmt.Errorf("backup failed: %w", err)
		}
		c.logger.Info("Backup completed: %s", backupDir)
	}

	// 执行清理
	return c.executeCleanup(*targetProject, options)
}

// CleanAll 清理所有项目的聊天记录
func (c *augmentCleaner) CleanAll(options CleanOptions) error {
	projects, err := c.ScanProjects()
	if err != nil {
		return fmt.Errorf("failed to scan projects: %w", err)
	}

	c.logger.Info("Starting cleanup for %d projects", len(projects))

	var errors []string
	for _, project := range projects {
		// 如果指定了目标项目列表，则只清理指定项目
		if len(options.TargetProjects) > 0 {
			found := false
			for _, target := range options.TargetProjects {
				if project.ProjectHash == target {
					found = true
					break
				}
			}
			if !found {
				continue
			}
		}

		if err := c.CleanProject(project.ProjectHash, options); err != nil {
			errorMsg := fmt.Sprintf("failed to clean project %s: %v", project.ProjectHash, err)
			errors = append(errors, errorMsg)
			c.logger.Error(errorMsg)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("cleanup completed with errors: %s", strings.Join(errors, "; "))
	}

	c.logger.Info("All projects cleaned successfully")
	return nil
}

// BackupProject 备份项目数据
func (c *augmentCleaner) BackupProject(projectHash, backupDir string) error {
	sourceDir := filepath.Join(c.baseDir, "projects", projectHash, "plugin-file-store")

	// 创建备份目录
	if err := os.MkdirAll(backupDir, 0755); err != nil {
		return fmt.Errorf("failed to create backup directory: %w", err)
	}

	// 递归复制文件
	return filepath.Walk(sourceDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 计算相对路径
		relPath, err := filepath.Rel(sourceDir, path)
		if err != nil {
			return err
		}

		destPath := filepath.Join(backupDir, relPath)

		if info.IsDir() {
			return os.MkdirAll(destPath, info.Mode())
		}

		// 复制文件
		return copyFile(path, destPath)
	})
}

// executeCleanup 执行具体的清理操作
func (c *augmentCleaner) executeCleanup(project ProjectInfo, options CleanOptions) error {
	var filesToDelete []string

	// 根据选项确定要删除的文件
	if options.CleanChatHistory {
		filesToDelete = append(filesToDelete, project.ChatFiles...)
	}
	if options.CleanImages {
		filesToDelete = append(filesToDelete, project.ImageFiles...)
	}
	if options.CleanMemories {
		filesToDelete = append(filesToDelete, project.MemoryFiles...)
	}

	// 时间过滤
	if options.OlderThan != nil {
		cutoffTime := time.Now().Add(-*options.OlderThan)
		filesToDelete = c.filterFilesByTime(filesToDelete, cutoffTime)
	}

	c.logger.Info("Project %s: %d files to be deleted", project.ProjectHash, len(filesToDelete))

	// 试运行模式
	if options.DryRun {
		for _, file := range filesToDelete {
			c.logger.Info("DRY RUN: would delete %s", file)
		}
		return nil
	}

	// 执行删除
	var errors []string
	for _, file := range filesToDelete {
		if err := os.Remove(file); err != nil {
			errorMsg := fmt.Sprintf("failed to delete %s: %v", file, err)
			errors = append(errors, errorMsg)
			c.logger.Error(errorMsg)
		} else {
			c.logger.Info("Deleted: %s", file)
		}
	}

	// 清理空目录
	c.cleanEmptyDirectories(project.StorePath)

	if len(errors) > 0 {
		return fmt.Errorf("cleanup completed with errors: %s", strings.Join(errors, "; "))
	}

	return nil
}

// filterFilesByTime 根据时间过滤文件
func (c *augmentCleaner) filterFilesByTime(files []string, cutoffTime time.Time) []string {
	var filtered []string
	for _, file := range files {
		if info, err := os.Stat(file); err == nil {
			if info.ModTime().Before(cutoffTime) {
				filtered = append(filtered, file)
			}
		}
	}
	return filtered
}

// cleanEmptyDirectories 清理空目录
func (c *augmentCleaner) cleanEmptyDirectories(rootDir string) {
	filepath.Walk(rootDir, func(path string, info os.FileInfo, err error) error {
		if err != nil || !info.IsDir() || path == rootDir {
			return err
		}

		// 检查目录是否为空
		if entries, err := os.ReadDir(path); err == nil && len(entries) == 0 {
			if err := os.Remove(path); err == nil {
				c.logger.Info("Removed empty directory: %s", path)
			}
		}

		return nil
	})
}

// copyFile 复制文件的辅助函数
func copyFile(src, dst string) error {
	// 创建目标目录
	if err := os.MkdirAll(filepath.Dir(dst), 0755); err != nil {
		return err
	}

	// 打开源文件
	srcFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer srcFile.Close()

	// 创建目标文件
	dstFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer dstFile.Close()

	// 复制文件内容
	_, err = io.Copy(dstFile, srcFile)
	if err != nil {
		return err
	}

	// 复制文件权限
	srcInfo, err := os.Stat(src)
	if err != nil {
		return err
	}

	return os.Chmod(dst, srcInfo.Mode())
}

// 使用示例和安全检查函数

// SafetyChecker 安全检查器
type SafetyChecker struct {
	allowedBasePaths []string
}

// NewSafetyChecker 创建安全检查器
func NewSafetyChecker() *SafetyChecker {
	return &SafetyChecker{
		allowedBasePaths: []string{".augmentcode"},
	}
}

// ValidatePath 验证路径是否安全
func (sc *SafetyChecker) ValidatePath(path string) error {
	absPath, err := filepath.Abs(path)
	if err != nil {
		return fmt.Errorf("failed to get absolute path: %w", err)
	}

	// 检查路径是否包含允许的基础路径
	for _, allowedBase := range sc.allowedBasePaths {
		if strings.Contains(absPath, allowedBase) {
			return nil
		}
	}

	return fmt.Errorf("path not in allowed directories: %s", absPath)
}

// ConfirmationPrompt 确认提示函数
func ConfirmationPrompt(message string) bool {
	fmt.Printf("%s (y/N): ", message)
	var response string
	fmt.Scanln(&response)
	return strings.ToLower(response) == "y" || strings.ToLower(response) == "yes"
}

// 使用示例
func ExampleUsage() {
	// 创建简单的控制台日志器
	logger := &ConsoleLogger{}

	// 创建清理器
	cleaner, err := NewAugmentCleaner(logger)
	if err != nil {
		logger.Error("Failed to create cleaner: %v", err)
		return
	}

	// 扫描项目
	projects, err := cleaner.ScanProjects()
	if err != nil {
		logger.Error("Failed to scan projects: %v", err)
		return
	}

	// 显示项目信息
	for _, project := range projects {
		logger.Info("Project: %s", project.ProjectHash)
		logger.Info("  Chat files: %d", len(project.ChatFiles))
		logger.Info("  Image files: %d", len(project.ImageFiles))
		logger.Info("  Memory files: %d", len(project.MemoryFiles))
		logger.Info("  Total size: %d bytes", project.TotalSize)
		logger.Info("  Last modified: %s", project.LastModified.Format("2006-01-02 15:04:05"))
	}

	// 配置清理选项
	options := CleanOptions{
		BackupBeforeClean: true,
		CleanChatHistory:  true,
		CleanImages:       true,
		CleanMemories:     false,                                    // 保留记忆文件
		DryRun:            true,                                     // 先试运行
		OlderThan:         &[]time.Duration{30 * 24 * time.Hour}[0], // 只清理30天前的文件
	}

	// 确认清理操作
	if ConfirmationPrompt("Do you want to proceed with cleanup?") {
		if err := cleaner.CleanAll(options); err != nil {
			logger.Error("Cleanup failed: %v", err)
		} else {
			logger.Info("Cleanup completed successfully")
		}
	}
}

// ConsoleLogger 简单的控制台日志实现
type ConsoleLogger struct{}

func (l *ConsoleLogger) Info(msg string, args ...interface{}) {
	fmt.Printf("[INFO] "+msg+"\n", args...)
}

func (l *ConsoleLogger) Warn(msg string, args ...interface{}) {
	fmt.Printf("[WARN] "+msg+"\n", args...)
}

func (l *ConsoleLogger) Error(msg string, args ...interface{}) {
	fmt.Printf("[ERROR] "+msg+"\n", args...)
}
