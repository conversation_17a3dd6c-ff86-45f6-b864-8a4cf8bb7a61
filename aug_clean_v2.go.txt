package main

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"io/fs"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"github.com/google/uuid"
	_ "github.com/mattn/go-sqlite3"
)

// 配置常量
const (
	AppName   = "AugmentCode Unlimited Go"
	Version   = "3.0.0"
	BackupDir = ".augment_cleaner_backups"
)

// JetBrains配置
var jetbrainsConfig = struct {
	IDFiles          []string
	AugmentPatterns  []string
	DatabasePatterns []string
	CacheDirs        []string
}{
	IDFiles:          []string{"PermanentDeviceId", "PermanentUserId"},
	AugmentPatterns:  []string{"%augment%", "%Augment%", "%AUGMENT%", "%device%", "%user%", "%machine%", "%telemetry%"},
	DatabasePatterns: []string{"*.db", "*.sqlite", "*.sqlite3"},
	CacheDirs:        []string{"caches", "logs", "system", "temp"},
}

// VSCode配置
var vscodeConfig = struct {
	TelemetryKeys []string
	Variants      []string
	DatabaseFiles []string
}{
	TelemetryKeys: []string{"telemetry.machineId", "telemetry.devDeviceId", "telemetry.macMachineId", "telemetry.sqmId"},
	Variants:      []string{"Code", "Code - Insiders", "VSCodium", "Cursor", "code-server"},
	DatabaseFiles: []string{"state.vscdb", "state.vscdb.backup"},
}

// 处理结果结构
type ProcessResult struct {
	Success        bool              `json:"success"`
	FilesProcessed []string          `json:"files_processed"`
	FilesFailed    []string          `json:"files_failed"`
	BackupsCreated []string          `json:"backups_created"`
	OldIDs         map[string]string `json:"old_ids"`
	NewIDs         map[string]string `json:"new_ids"`
	RecordsCleaned int               `json:"records_cleaned"`
	Errors         []string          `json:"errors"`
}

// 路径管理器
type PathManager struct {
	ConfigDir string
	DataDir   string
	HomeDir   string
}

// 备份管理器
type BackupManager struct {
	BackupDir string
}

// 主处理器
type AugmentCleaner struct {
	PathManager   *PathManager
	BackupManager *BackupManager
}

// 初始化路径管理器
func NewPathManager() *PathManager {
	pm := &PathManager{}

	switch runtime.GOOS {
	case "windows":
		pm.ConfigDir = os.Getenv("APPDATA")
		pm.DataDir = os.Getenv("LOCALAPPDATA")
	case "darwin":
		homeDir, _ := os.UserHomeDir()
		pm.ConfigDir = filepath.Join(homeDir, "Library", "Application Support")
		pm.DataDir = pm.ConfigDir
	default: // Linux
		homeDir, _ := os.UserHomeDir()
		pm.ConfigDir = filepath.Join(homeDir, ".config")
		pm.DataDir = filepath.Join(homeDir, ".local", "share")
	}

	pm.HomeDir, _ = os.UserHomeDir()
	return pm
}

// 初始化备份管理器
func NewBackupManager() *BackupManager {
	homeDir, _ := os.UserHomeDir()
	backupDir := filepath.Join(homeDir, BackupDir)

	// 创建备份目录
	os.MkdirAll(backupDir, 0755)

	return &BackupManager{BackupDir: backupDir}
}

// 创建文件备份
func (bm *BackupManager) CreateFileBackup(filePath, backupName string) (string, error) {
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return "", fmt.Errorf("file does not exist: %s", filePath)
	}

	timestamp := time.Now().Format("20060102_150405")
	if backupName == "" {
		backupName = filepath.Base(filePath)
	}

	backupPath := filepath.Join(bm.BackupDir, fmt.Sprintf("%s_%s.bak", backupName, timestamp))

	// 复制文件
	input, err := os.ReadFile(filePath)
	if err != nil {
		return "", err
	}

	err = os.WriteFile(backupPath, input, 0644)
	if err != nil {
		return "", err
	}

	log.Printf("Created backup: %s -> %s", filePath, backupPath)
	return backupPath, nil
}

// 恢复文件备份
func (bm *BackupManager) RestoreFileBackup(backupPath, targetPath string) error {
	if _, err := os.Stat(backupPath); os.IsNotExist(err) {
		return fmt.Errorf("backup file does not exist: %s", backupPath)
	}

	// 读取备份文件
	backupData, err := os.ReadFile(backupPath)
	if err != nil {
		return fmt.Errorf("failed to read backup file: %v", err)
	}

	// 确保目标目录存在
	os.MkdirAll(filepath.Dir(targetPath), 0755)

	// 解锁目标文件（如果已锁定）
	unlockFile(targetPath)

	// 恢复文件
	err = os.WriteFile(targetPath, backupData, 0644)
	if err != nil {
		return fmt.Errorf("failed to restore file: %v", err)
	}

	log.Printf("Restored file: %s <- %s", targetPath, backupPath)
	return nil
}

// 列出备份文件
func (bm *BackupManager) ListBackups() ([]string, error) {
	var backups []string

	files, err := os.ReadDir(bm.BackupDir)
	if err != nil {
		return nil, err
	}

	for _, file := range files {
		if !file.IsDir() && strings.HasSuffix(file.Name(), ".bak") {
			backups = append(backups, filepath.Join(bm.BackupDir, file.Name()))
		}
	}

	return backups, nil
}

// 清理旧备份
func (bm *BackupManager) CleanupOldBackups(maxBackups int) error {
	backups, err := bm.ListBackups()
	if err != nil {
		return err
	}

	if len(backups) <= maxBackups {
		return nil
	}

	// 按修改时间排序，删除最旧的备份
	type backupInfo struct {
		path    string
		modTime time.Time
	}

	var backupInfos []backupInfo
	for _, backup := range backups {
		info, err := os.Stat(backup)
		if err != nil {
			continue
		}
		backupInfos = append(backupInfos, backupInfo{
			path:    backup,
			modTime: info.ModTime(),
		})
	}

	// 简单排序（最新的在前）
	for i := 0; i < len(backupInfos)-1; i++ {
		for j := i + 1; j < len(backupInfos); j++ {
			if backupInfos[i].modTime.Before(backupInfos[j].modTime) {
				backupInfos[i], backupInfos[j] = backupInfos[j], backupInfos[i]
			}
		}
	}

	// 删除多余的备份
	for i := maxBackups; i < len(backupInfos); i++ {
		if err := os.Remove(backupInfos[i].path); err != nil {
			log.Printf("Failed to remove old backup %s: %v", backupInfos[i].path, err)
		} else {
			log.Printf("Removed old backup: %s", backupInfos[i].path)
		}
	}

	return nil
}

// 获取JetBrains配置目录
func (pm *PathManager) GetJetBrainsConfigDir() string {
	jetbrainsPath := filepath.Join(pm.ConfigDir, "JetBrains")
	if _, err := os.Stat(jetbrainsPath); err == nil {
		return jetbrainsPath
	}
	return ""
}

// 获取JetBrains ID文件列表
func (pm *PathManager) GetJetBrainsIDFiles() []string {
	jetbrainsDir := pm.GetJetBrainsConfigDir()
	if jetbrainsDir == "" {
		return nil
	}

	var idFiles []string
	for _, fileName := range jetbrainsConfig.IDFiles {
		filePath := filepath.Join(jetbrainsDir, fileName)
		idFiles = append(idFiles, filePath)
	}

	return idFiles
}

// 获取JetBrains数据库文件
func (pm *PathManager) GetJetBrainsDatabaseFiles() []string {
	jetbrainsDir := pm.GetJetBrainsConfigDir()
	if jetbrainsDir == "" {
		return nil
	}

	var dbFiles []string

	// 遍历所有子目录查找数据库文件
	filepath.WalkDir(jetbrainsDir, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return nil
		}

		if !d.IsDir() {
			ext := strings.ToLower(filepath.Ext(path))
			if ext == ".db" || ext == ".sqlite" || ext == ".sqlite3" {
				dbFiles = append(dbFiles, path)
			}
		}
		return nil
	})

	return dbFiles
}

// 获取VSCode目录列表
func (pm *PathManager) GetVSCodeDirectories() []string {
	var vscodeDirectories []string

	for _, variant := range vscodeConfig.Variants {
		variantPath := filepath.Join(pm.ConfigDir, variant)
		if _, err := os.Stat(variantPath); err == nil {
			// 查找User目录
			userPath := filepath.Join(variantPath, "User")
			if _, err := os.Stat(userPath); err == nil {
				vscodeDirectories = append(vscodeDirectories, userPath)
			}
		}
	}

	return vscodeDirectories
}

// 获取VSCode工作区存储目录
func (pm *PathManager) GetVSCodeWorkspaceStorageDirectories() []string {
	var workspaceDirectories []string

	for _, variant := range vscodeConfig.Variants {
		variantPath := filepath.Join(pm.ConfigDir, variant)
		if _, err := os.Stat(variantPath); err == nil {
			// 查找User/workspaceStorage目录
			workspacePath := filepath.Join(variantPath, "User", "workspaceStorage")
			if _, err := os.Stat(workspacePath); err == nil {
				workspaceDirectories = append(workspaceDirectories, workspacePath)
			}
		}
	}

	return workspaceDirectories
}

// 获取VSCode缓存目录
func (pm *PathManager) GetVSCodeCacheDirectories() []string {
	var cacheDirectories []string

	for _, variant := range vscodeConfig.Variants {
		variantPath := filepath.Join(pm.DataDir, variant)
		if _, err := os.Stat(variantPath); err == nil {
			// 查找缓存相关目录
			cacheDirs := []string{"logs", "CachedExtensions", "CachedExtensionVSIXs", "User/History"}
			for _, cacheDir := range cacheDirs {
				cachePath := filepath.Join(variantPath, cacheDir)
				if _, err := os.Stat(cachePath); err == nil {
					cacheDirectories = append(cacheDirectories, cachePath)
				}
			}
		}
	}

	return cacheDirectories
}

// 生成新的UUID
func generateUUID() string {
	return strings.ToLower(uuid.New().String())
}

// 生成机器ID（64字符十六进制）
func generateMachineID() string {
	return strings.ToLower(uuid.New().String() + uuid.New().String())[:64]
}

// 智能ID生成 - 根据键名生成对应格式的ID
func generateIDForKey(key string) string {
	key = strings.ToLower(key)

	// 机器ID相关键值
	if strings.Contains(key, "machineid") || strings.Contains(key, "machine_id") {
		return generateMachineID()
	}

	// 设备ID相关键值
	if strings.Contains(key, "deviceid") || strings.Contains(key, "device_id") {
		return generateUUID()
	}

	// 用户ID相关键值
	if strings.Contains(key, "userid") || strings.Contains(key, "user_id") {
		return generateUUID()
	}

	// 遥测ID相关键值
	if strings.Contains(key, "telemetry") || strings.Contains(key, "sqm") {
		return generateUUID()
	}

	// 默认返回UUID
	return generateUUID()
}

// 验证ID格式
func validateIDFormat(id, expectedType string) bool {
	if id == "" {
		return false
	}

	switch strings.ToLower(expectedType) {
	case "uuid":
		// UUID格式: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
		if len(id) == 36 && strings.Count(id, "-") == 4 {
			return true
		}
	case "machineid":
		// 机器ID格式: 64字符十六进制
		if len(id) == 64 {
			for _, char := range id {
				if !((char >= '0' && char <= '9') || (char >= 'a' && char <= 'f')) {
					return false
				}
			}
			return true
		}
	}

	return false
}

// 检查是否为SQLite数据库
func isSQLiteDatabase(filePath string) bool {
	file, err := os.Open(filePath)
	if err != nil {
		return false
	}
	defer file.Close()

	header := make([]byte, 16)
	n, err := file.Read(header)
	if err != nil || n < 16 {
		return false
	}

	return string(header[:15]) == "SQLite format 3"
}

// 锁定文件（设置只读）
func lockFile(filePath string) error {
	switch runtime.GOOS {
	case "windows":
		return os.Chmod(filePath, 0444)
	default:
		return os.Chmod(filePath, 0444)
	}
}

// 高级文件锁定（平台特定实现）
func lockFileAdvanced(filePath string) error {
	switch runtime.GOOS {
	case "windows":
		// 使用 attrib +R 命令设置只读属性
		cmd := exec.Command("attrib", "+R", filePath)
		if err := cmd.Run(); err != nil {
			// 如果命令失败，回退到基础权限设置
			return os.Chmod(filePath, 0444)
		}
		return nil
	case "darwin":
		// macOS: 先设置只读权限，再设置不可变标志
		if err := os.Chmod(filePath, 0444); err != nil {
			return err
		}
		// 使用 chflags uchg 设置不可变标志
		cmd := exec.Command("chflags", "uchg", filePath)
		if err := cmd.Run(); err != nil {
			log.Printf("Warning: Failed to set immutable flag for %s: %v", filePath, err)
		}
		return nil
	default:
		// Linux: 使用 chmod 设置只读权限
		return os.Chmod(filePath, 0444)
	}
}

// 解锁文件
func unlockFile(filePath string) error {
	return os.Chmod(filePath, 0644)
}

// 高级文件解锁（平台特定实现）
func unlockFileAdvanced(filePath string) error {
	switch runtime.GOOS {
	case "windows":
		// 使用 attrib -R 命令移除只读属性
		cmd := exec.Command("attrib", "-R", filePath)
		if err := cmd.Run(); err != nil {
			// 如果命令失败，回退到基础权限设置
			return os.Chmod(filePath, 0644)
		}
		return nil
	case "darwin":
		// macOS: 先移除不可变标志，再设置可写权限
		cmd := exec.Command("chflags", "nouchg", filePath)
		if err := cmd.Run(); err != nil {
			log.Printf("Warning: Failed to remove immutable flag for %s: %v", filePath, err)
		}
		return os.Chmod(filePath, 0644)
	default:
		// Linux: 使用 chmod 设置可写权限
		return os.Chmod(filePath, 0644)
	}
}

// 检查文件是否被锁定
func isFileLocked(filePath string) bool {
	info, err := os.Stat(filePath)
	if err != nil {
		return false
	}

	// 检查文件权限
	mode := info.Mode()

	// 如果文件没有写权限，认为是锁定状态
	return mode&0200 == 0 // 检查用户写权限位
}

// 批量锁定文件
func lockMultipleFiles(filePaths []string, useAdvanced bool) map[string]error {
	results := make(map[string]error)

	for _, filePath := range filePaths {
		if useAdvanced {
			results[filePath] = lockFileAdvanced(filePath)
		} else {
			results[filePath] = lockFile(filePath)
		}
	}

	return results
}

// 处理JetBrains ID文件
func (ac *AugmentCleaner) processJetBrainsIDFile(filePath string, createBackup, lockFiles bool) map[string]interface{} {
	result := map[string]interface{}{
		"success":     false,
		"backup_path": "",
		"old_id":      "",
		"new_id":      "",
		"error":       "",
	}

	log.Printf("Processing JetBrains ID file: %s", filePath)

	// 读取旧ID
	var oldID string
	if _, err := os.Stat(filePath); err == nil {
		if data, err := os.ReadFile(filePath); err == nil {
			oldID = strings.TrimSpace(string(data))
			result["old_id"] = oldID
		}
	}

	// 创建备份
	if createBackup && oldID != "" {
		backupPath, err := ac.BackupManager.CreateFileBackup(filePath, fmt.Sprintf("jetbrains_%s", filepath.Base(filePath)))
		if err != nil {
			log.Printf("Failed to create backup for %s: %v", filePath, err)
		} else {
			result["backup_path"] = backupPath
		}
	}

	// 生成新ID
	newID := generateUUID()
	result["new_id"] = newID

	// 确保目录存在
	os.MkdirAll(filepath.Dir(filePath), 0755)

	// 解锁文件（如果已锁定）
	unlockFile(filePath)

	// 写入新ID
	err := os.WriteFile(filePath, []byte(newID), 0644)
	if err != nil {
		result["error"] = fmt.Sprintf("Failed to write new ID: %v", err)
		return result
	}

	// 锁定文件
	if lockFiles {
		if err := lockFile(filePath); err != nil {
			log.Printf("Failed to lock file %s: %v", filePath, err)
		}
	}

	result["success"] = true
	log.Printf("Successfully processed %s: %s -> %s", filePath, oldID, newID)
	return result
}

// 清理SQLite数据库
func (ac *AugmentCleaner) cleanSQLiteDatabase(dbPath string) (int, error) {
	if !isSQLiteDatabase(dbPath) {
		return 0, nil
	}

	db, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		return 0, err
	}
	defer db.Close()

	recordsCleaned := 0

	// 获取所有表名
	rows, err := db.Query("SELECT name FROM sqlite_master WHERE type='table'")
	if err != nil {
		return 0, err
	}
	defer rows.Close()

	var tables []string
	for rows.Next() {
		var tableName string
		if err := rows.Scan(&tableName); err != nil {
			continue
		}
		tables = append(tables, tableName)
	}

	// 清理每个表
	for _, tableName := range tables {
		// 获取表结构
		columnRows, err := db.Query(fmt.Sprintf("PRAGMA table_info(%s)", tableName))
		if err != nil {
			continue
		}

		var textColumns []string
		for columnRows.Next() {
			var cid int
			var name, dataType string
			var notNull, pk int
			var defaultValue interface{}

			if err := columnRows.Scan(&cid, &name, &dataType, &notNull, &defaultValue, &pk); err != nil {
				continue
			}

			dataType = strings.ToUpper(dataType)
			if strings.Contains(dataType, "TEXT") || strings.Contains(dataType, "VARCHAR") || strings.Contains(dataType, "CHAR") {
				textColumns = append(textColumns, name)
			}
		}
		columnRows.Close()

		// 清理匹配的记录
		for _, column := range textColumns {
			for _, pattern := range jetbrainsConfig.AugmentPatterns {
				// 计算要删除的记录数
				countQuery := fmt.Sprintf("SELECT COUNT(*) FROM %s WHERE %s LIKE ?", tableName, column)
				var count int
				if err := db.QueryRow(countQuery, pattern).Scan(&count); err != nil {
					continue
				}

				if count > 0 {
					// 删除记录
					deleteQuery := fmt.Sprintf("DELETE FROM %s WHERE %s LIKE ?", tableName, column)
					if _, err := db.Exec(deleteQuery, pattern); err != nil {
						log.Printf("Failed to delete from %s.%s: %v", tableName, column, err)
						continue
					}

					recordsCleaned += count
					log.Printf("Cleaned %d records from %s.%s matching %s", count, tableName, column, pattern)
				}
			}
		}
	}

	return recordsCleaned, nil
}

// 处理JetBrains数据库文件
func (ac *AugmentCleaner) processJetBrainsDatabaseFile(dbPath string, createBackup bool) map[string]interface{} {
	result := map[string]interface{}{
		"success":         false,
		"backup_path":     "",
		"records_cleaned": 0,
		"error":           "",
	}

	log.Printf("Processing JetBrains database: %s", dbPath)

	if _, err := os.Stat(dbPath); os.IsNotExist(err) {
		result["error"] = "Database file does not exist"
		return result
	}

	// 创建备份
	if createBackup {
		backupPath, err := ac.BackupManager.CreateFileBackup(dbPath, fmt.Sprintf("jetbrains_db_%s", filepath.Base(dbPath)))
		if err != nil {
			log.Printf("Failed to create backup for %s: %v", dbPath, err)
		} else {
			result["backup_path"] = backupPath
		}
	}

	// 清理数据库
	recordsCleaned, err := ac.cleanSQLiteDatabase(dbPath)
	if err != nil {
		result["error"] = fmt.Sprintf("Database cleaning failed: %v", err)
		return result
	}

	result["records_cleaned"] = recordsCleaned
	result["success"] = true
	log.Printf("Successfully cleaned %d records from %s", recordsCleaned, dbPath)
	return result
}

// 处理VSCode存储文件
func (ac *AugmentCleaner) processVSCodeStorageFile(filePath string, createBackup, lockFiles bool) map[string]interface{} {
	result := map[string]interface{}{
		"success":     false,
		"backup_path": "",
		"old_ids":     make(map[string]string),
		"new_ids":     make(map[string]string),
		"error":       "",
	}

	log.Printf("Processing VSCode storage file: %s", filePath)

	// 检查文件类型
	if strings.HasSuffix(filePath, ".json") {
		return ac.processVSCodeJSON(filePath, createBackup, lockFiles)
	} else if strings.HasSuffix(filePath, ".vscdb") {
		return ac.processVSCodeDatabase(filePath, createBackup, lockFiles)
	}

	result["error"] = "Unsupported file type"
	return result
}

// 处理VSCode JSON配置文件
func (ac *AugmentCleaner) processVSCodeJSON(filePath string, createBackup, lockFiles bool) map[string]interface{} {
	result := map[string]interface{}{
		"success":     false,
		"backup_path": "",
		"old_ids":     make(map[string]string),
		"new_ids":     make(map[string]string),
		"error":       "",
	}

	// 读取JSON文件
	data, err := os.ReadFile(filePath)
	if err != nil {
		result["error"] = fmt.Sprintf("Failed to read file: %v", err)
		return result
	}

	var jsonData map[string]interface{}
	if err := json.Unmarshal(data, &jsonData); err != nil {
		result["error"] = fmt.Sprintf("Failed to parse JSON: %v", err)
		return result
	}

	// 创建备份
	if createBackup {
		backupPath, err := ac.BackupManager.CreateFileBackup(filePath, fmt.Sprintf("vscode_%s", filepath.Base(filePath)))
		if err != nil {
			log.Printf("Failed to create backup for %s: %v", filePath, err)
		} else {
			result["backup_path"] = backupPath
		}
	}

	// 处理遥测键值
	oldIDs := make(map[string]string)
	newIDs := make(map[string]string)
	modified := false

	for _, key := range vscodeConfig.TelemetryKeys {
		if oldValue, exists := jsonData[key]; exists {
			oldIDs[key] = fmt.Sprintf("%v", oldValue)

			// 生成新ID
			var newID string
			if strings.Contains(key, "machineId") {
				newID = generateMachineID()
			} else {
				newID = generateUUID()
			}

			jsonData[key] = newID
			newIDs[key] = newID
			modified = true

			log.Printf("Updated %s: %s -> %s", key, oldIDs[key], newID)
		}
	}

	// 写入修改后的JSON
	if modified {
		unlockFile(filePath)

		newData, err := json.MarshalIndent(jsonData, "", "  ")
		if err != nil {
			result["error"] = fmt.Sprintf("Failed to marshal JSON: %v", err)
			return result
		}

		if err := os.WriteFile(filePath, newData, 0644); err != nil {
			result["error"] = fmt.Sprintf("Failed to write file: %v", err)
			return result
		}

		if lockFiles {
			lockFile(filePath)
		}
	}

	result["success"] = true
	result["old_ids"] = oldIDs
	result["new_ids"] = newIDs
	return result
}

// 处理VSCode数据库文件
func (ac *AugmentCleaner) processVSCodeDatabase(filePath string, createBackup, lockFiles bool) map[string]interface{} {
	result := map[string]interface{}{
		"success":         false,
		"backup_path":     "",
		"old_ids":         make(map[string]string),
		"new_ids":         make(map[string]string),
		"records_cleaned": 0,
		"error":           "",
	}

	log.Printf("Processing VSCode database: %s", filePath)

	// 创建备份
	if createBackup {
		backupPath, err := ac.BackupManager.CreateFileBackup(filePath, fmt.Sprintf("vscode_db_%s", filepath.Base(filePath)))
		if err != nil {
			log.Printf("Failed to create backup for %s: %v", filePath, err)
		} else {
			result["backup_path"] = backupPath
		}
	}

	// 清理数据库中的遥测数据
	recordsCleaned, err := ac.cleanVSCodeDatabase(filePath)
	if err != nil {
		result["error"] = fmt.Sprintf("Database cleaning failed: %v", err)
		return result
	}

	result["records_cleaned"] = recordsCleaned
	result["success"] = true
	return result
}

// 清理VSCode数据库
func (ac *AugmentCleaner) cleanVSCodeDatabase(dbPath string) (int, error) {
	if !isSQLiteDatabase(dbPath) {
		return 0, nil
	}

	db, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		return 0, err
	}
	defer db.Close()

	recordsCleaned := 0

	// VSCode数据库通常有ItemTable表
	patterns := []string{"%augment%", "%Augment%", "%AUGMENT%", "%telemetry%", "%machine%", "%device%"}

	for _, pattern := range patterns {
		// 计算要删除的记录数
		var count int
		countQuery := "SELECT COUNT(*) FROM ItemTable WHERE key LIKE ?"
		if err := db.QueryRow(countQuery, pattern).Scan(&count); err != nil {
			continue
		}

		if count > 0 {
			// 删除记录
			deleteQuery := "DELETE FROM ItemTable WHERE key LIKE ?"
			if _, err := db.Exec(deleteQuery, pattern); err != nil {
				log.Printf("Failed to delete records matching %s: %v", pattern, err)
				continue
			}

			recordsCleaned += count
			log.Printf("Cleaned %d records matching %s", count, pattern)
		}
	}

	return recordsCleaned, nil
}

// 处理所有JetBrains IDE
func (ac *AugmentCleaner) ProcessJetBrainsIDEs(createBackups, lockFiles, cleanDatabases bool) *ProcessResult {
	result := &ProcessResult{
		Success:        false,
		FilesProcessed: []string{},
		FilesFailed:    []string{},
		BackupsCreated: []string{},
		OldIDs:         make(map[string]string),
		NewIDs:         make(map[string]string),
		RecordsCleaned: 0,
		Errors:         []string{},
	}

	log.Println("Starting JetBrains IDE processing...")

	// 获取JetBrains配置目录
	jetbrainsDir := ac.PathManager.GetJetBrainsConfigDir()
	if jetbrainsDir == "" {
		result.Errors = append(result.Errors, "JetBrains configuration directory not found")
		return result
	}

	log.Printf("Found JetBrains directory: %s", jetbrainsDir)

	// 处理ID文件
	idFiles := ac.PathManager.GetJetBrainsIDFiles()
	for _, filePath := range idFiles {
		fileResult := ac.processJetBrainsIDFile(filePath, createBackups, lockFiles)

		if fileResult["success"].(bool) {
			result.FilesProcessed = append(result.FilesProcessed, filePath)
			if backupPath := fileResult["backup_path"].(string); backupPath != "" {
				result.BackupsCreated = append(result.BackupsCreated, backupPath)
			}
			if oldID := fileResult["old_id"].(string); oldID != "" {
				result.OldIDs[filepath.Base(filePath)] = oldID
			}
			if newID := fileResult["new_id"].(string); newID != "" {
				result.NewIDs[filepath.Base(filePath)] = newID
			}
		} else {
			result.FilesFailed = append(result.FilesFailed, filePath)
			if errorMsg := fileResult["error"].(string); errorMsg != "" {
				result.Errors = append(result.Errors, fmt.Sprintf("%s: %s", filepath.Base(filePath), errorMsg))
			}
		}
	}

	// 处理数据库文件
	if cleanDatabases {
		dbFiles := ac.PathManager.GetJetBrainsDatabaseFiles()
		for _, dbPath := range dbFiles {
			dbResult := ac.processJetBrainsDatabaseFile(dbPath, createBackups)

			if dbResult["success"].(bool) {
				if backupPath := dbResult["backup_path"].(string); backupPath != "" {
					result.BackupsCreated = append(result.BackupsCreated, backupPath)
				}
				result.RecordsCleaned += dbResult["records_cleaned"].(int)
			} else {
				if errorMsg := dbResult["error"].(string); errorMsg != "" {
					result.Errors = append(result.Errors, fmt.Sprintf("%s: %s", filepath.Base(dbPath), errorMsg))
				}
			}
		}
	}

	// 判断整体成功
	result.Success = len(result.FilesProcessed) > 0

	if result.Success {
		log.Printf("Successfully processed %d JetBrains files", len(result.FilesProcessed))
	} else {
		log.Println("Failed to process any JetBrains files")
	}

	return result
}

// 处理所有VSCode安装
func (ac *AugmentCleaner) ProcessVSCodeInstallations(createBackups, lockFiles bool) *ProcessResult {
	result := &ProcessResult{
		Success:        false,
		FilesProcessed: []string{},
		FilesFailed:    []string{},
		BackupsCreated: []string{},
		OldIDs:         make(map[string]string),
		NewIDs:         make(map[string]string),
		RecordsCleaned: 0,
		Errors:         []string{},
	}

	log.Println("Starting VSCode processing...")

	// 获取VSCode目录
	vscodeDirectories := ac.PathManager.GetVSCodeDirectories()
	if len(vscodeDirectories) == 0 {
		result.Errors = append(result.Errors, "No VSCode installations found")
		return result
	}

	log.Printf("Found %d VSCode directories", len(vscodeDirectories))

	// 处理每个VSCode目录
	for _, vscodeDir := range vscodeDirectories {
		log.Printf("Processing VSCode directory: %s", vscodeDir)

		// 处理storage.json
		storageFile := filepath.Join(vscodeDir, "storage.json")
		if _, err := os.Stat(storageFile); err == nil {
			storageResult := ac.processVSCodeJSON(storageFile, createBackups, lockFiles)
			if storageResult["success"].(bool) {
				result.FilesProcessed = append(result.FilesProcessed, storageFile)
				if backupPath := storageResult["backup_path"].(string); backupPath != "" {
					result.BackupsCreated = append(result.BackupsCreated, backupPath)
				}

				// 合并ID映射
				if oldIDs, ok := storageResult["old_ids"].(map[string]string); ok {
					for k, v := range oldIDs {
						result.OldIDs[k] = v
					}
				}
				if newIDs, ok := storageResult["new_ids"].(map[string]string); ok {
					for k, v := range newIDs {
						result.NewIDs[k] = v
					}
				}
			} else {
				result.FilesFailed = append(result.FilesFailed, storageFile)
				if errorMsg := storageResult["error"].(string); errorMsg != "" {
					result.Errors = append(result.Errors, fmt.Sprintf("storage.json: %s", errorMsg))
				}
			}
		}

		// 处理state.vscdb
		dbFile := filepath.Join(vscodeDir, "state.vscdb")
		if _, err := os.Stat(dbFile); err == nil {
			dbResult := ac.processVSCodeDatabase(dbFile, createBackups, lockFiles)
			if dbResult["success"].(bool) {
				result.FilesProcessed = append(result.FilesProcessed, dbFile)
				if backupPath := dbResult["backup_path"].(string); backupPath != "" {
					result.BackupsCreated = append(result.BackupsCreated, backupPath)
				}
				result.RecordsCleaned += dbResult["records_cleaned"].(int)
			} else {
				result.FilesFailed = append(result.FilesFailed, dbFile)
				if errorMsg := dbResult["error"].(string); errorMsg != "" {
					result.Errors = append(result.Errors, fmt.Sprintf("state.vscdb: %s", errorMsg))
				}
			}
		}
	}

	// 判断整体成功
	result.Success = len(result.FilesProcessed) > 0

	if result.Success {
		log.Printf("Successfully processed %d VSCode files", len(result.FilesProcessed))
	} else {
		log.Println("Failed to process any VSCode files")
	}

	return result
}

// 显示系统信息
func (ac *AugmentCleaner) ShowSystemInfo() {
	fmt.Printf("=== %s v%s ===\n", AppName, Version)
	fmt.Printf("Platform: %s/%s\n", runtime.GOOS, runtime.GOARCH)
	fmt.Printf("Go Version: %s\n\n", runtime.Version())

	// JetBrains信息
	fmt.Println("=== JetBrains IDE Detection ===")
	jetbrainsDir := ac.PathManager.GetJetBrainsConfigDir()
	if jetbrainsDir != "" {
		fmt.Printf("✅ JetBrains Config Directory: %s\n", jetbrainsDir)

		idFiles := ac.PathManager.GetJetBrainsIDFiles()
		fmt.Printf("📁 ID Files (%d):\n", len(idFiles))
		for _, filePath := range idFiles {
			if _, err := os.Stat(filePath); err == nil {
				fmt.Printf("  ✅ %s (exists)\n", filePath)
			} else {
				fmt.Printf("  ❌ %s (missing)\n", filePath)
			}
		}

		dbFiles := ac.PathManager.GetJetBrainsDatabaseFiles()
		fmt.Printf("🗄️ Database Files (%d):\n", len(dbFiles))
		for _, dbPath := range dbFiles {
			fmt.Printf("  📄 %s\n", dbPath)
		}
	} else {
		fmt.Println("❌ JetBrains not found")
	}

	fmt.Println()

	// VSCode信息
	fmt.Println("=== VSCode Detection ===")
	vscodeDirectories := ac.PathManager.GetVSCodeDirectories()
	if len(vscodeDirectories) > 0 {
		fmt.Printf("✅ VSCode Installations (%d):\n", len(vscodeDirectories))
		for _, vscodeDir := range vscodeDirectories {
			fmt.Printf("  📁 %s\n", vscodeDir)

			// 检查关键文件
			storageFile := filepath.Join(vscodeDir, "storage.json")
			if _, err := os.Stat(storageFile); err == nil {
				fmt.Printf("    ✅ storage.json\n")
			} else {
				fmt.Printf("    ❌ storage.json (missing)\n")
			}

			dbFile := filepath.Join(vscodeDir, "state.vscdb")
			if _, err := os.Stat(dbFile); err == nil {
				fmt.Printf("    ✅ state.vscdb\n")
			} else {
				fmt.Printf("    ❌ state.vscdb (missing)\n")
			}
		}
	} else {
		fmt.Println("❌ VSCode not found")
	}

	fmt.Println()
}

// 显示当前ID信息
func (ac *AugmentCleaner) ShowCurrentIDs() {
	fmt.Println("=== Current ID Information ===")

	// JetBrains IDs
	fmt.Println("JetBrains IDs:")
	idFiles := ac.PathManager.GetJetBrainsIDFiles()
	for _, filePath := range idFiles {
		if data, err := os.ReadFile(filePath); err == nil {
			currentID := strings.TrimSpace(string(data))
			idType := "UUID"
			if len(currentID) == 64 {
				idType = "MachineID"
			}
			fmt.Printf("  %s: %s (%s)\n", filepath.Base(filePath), currentID, idType)
		} else {
			fmt.Printf("  %s: (not found)\n", filepath.Base(filePath))
		}
	}

	fmt.Println()

	// VSCode IDs
	fmt.Println("VSCode IDs:")
	vscodeDirectories := ac.PathManager.GetVSCodeDirectories()
	for _, vscodeDir := range vscodeDirectories {
		storageFile := filepath.Join(vscodeDir, "storage.json")
		if data, err := os.ReadFile(storageFile); err == nil {
			var jsonData map[string]interface{}
			if json.Unmarshal(data, &jsonData) == nil {
				fmt.Printf("  %s:\n", filepath.Base(filepath.Dir(vscodeDir)))
				for _, key := range vscodeConfig.TelemetryKeys {
					if value, exists := jsonData[key]; exists {
						idStr := fmt.Sprintf("%v", value)
						idType := "UUID"
						if len(idStr) == 64 {
							idType = "MachineID"
						}
						fmt.Printf("    %s: %s (%s)\n", key, idStr, idType)
					} else {
						fmt.Printf("    %s: (not set)\n", key)
					}
				}
			}
		} else {
			fmt.Printf("  %s: (storage.json not found)\n", filepath.Base(filepath.Dir(vscodeDir)))
		}
	}

	fmt.Println()
}

// 获取当前JetBrains ID状态
func (ac *AugmentCleaner) GetCurrentJetBrainsIDs() map[string]string {
	currentIDs := make(map[string]string)

	idFiles := ac.PathManager.GetJetBrainsIDFiles()
	for _, filePath := range idFiles {
		if data, err := os.ReadFile(filePath); err == nil {
			currentID := strings.TrimSpace(string(data))
			currentIDs[filepath.Base(filePath)] = currentID
		}
	}

	return currentIDs
}

// 获取当前VSCode ID状态
func (ac *AugmentCleaner) GetCurrentVSCodeIDs() map[string]map[string]string {
	currentIDs := make(map[string]map[string]string)

	vscodeDirectories := ac.PathManager.GetVSCodeDirectories()
	for _, vscodeDir := range vscodeDirectories {
		storageFile := filepath.Join(vscodeDir, "storage.json")
		if data, err := os.ReadFile(storageFile); err == nil {
			var jsonData map[string]interface{}
			if json.Unmarshal(data, &jsonData) == nil {
				variantName := filepath.Base(filepath.Dir(vscodeDir))
				currentIDs[variantName] = make(map[string]string)

				for _, key := range vscodeConfig.TelemetryKeys {
					if value, exists := jsonData[key]; exists {
						currentIDs[variantName][key] = fmt.Sprintf("%v", value)
					}
				}
			}
		}
	}

	return currentIDs
}

// 打印使用帮助
func printUsage() {
	fmt.Printf(`%s v%s - AugmentCode设备限制绕过工具

用法:
  %s [选项]

选项:
  -h, --help              显示此帮助信息
  -v, --version           显示版本信息
  -i, --info              显示系统检测信息
  -c, --current-ids       显示当前ID信息
  -j, --jetbrains-only    仅处理JetBrains IDE
  -s, --vscode-only       仅处理VSCode系列
  --no-backup             跳过备份创建（更快但有风险）
  --no-lock               跳过文件锁定
  --no-database           跳过数据库清理
  --verbose               显示详细日志

示例:
  %s                      # 处理所有IDE（推荐）
  %s -i                   # 查看系统信息
  %s -j                   # 仅处理JetBrains
  %s -s --no-backup       # 仅处理VSCode，跳过备份

注意:
  - 运行前请关闭所有相关IDE
  - 建议保持默认设置（创建备份、锁定文件）
  - 备份文件保存在 ~/.augment_cleaner_backups/

`, AppName, Version, os.Args[0], os.Args[0], os.Args[0], os.Args[0], os.Args[0])
}

// 主函数
func main() {
	// 解析命令行参数
	args := os.Args[1:]

	var (
		showHelp       = false
		showVersion    = false
		showInfo       = false
		showCurrentIDs = false
		jetbrainsOnly  = false
		vscodeOnly     = false
		createBackups  = true
		lockFiles      = true
		cleanDatabase  = true
		verbose        = false
	)

	for _, arg := range args {
		switch arg {
		case "-h", "--help":
			showHelp = true
		case "-v", "--version":
			showVersion = true
		case "-i", "--info":
			showInfo = true
		case "-c", "--current-ids":
			showCurrentIDs = true
		case "-j", "--jetbrains-only":
			jetbrainsOnly = true
		case "-s", "--vscode-only":
			vscodeOnly = true
		case "--no-backup":
			createBackups = false
		case "--no-lock":
			lockFiles = false
		case "--no-database":
			cleanDatabase = false
		case "--verbose":
			verbose = true
		}
	}

	// 处理帮助和版本信息
	if showHelp {
		printUsage()
		return
	}

	if showVersion {
		fmt.Printf("%s v%s\n", AppName, Version)
		return
	}

	// 设置日志级别
	if !verbose {
		log.SetOutput(os.Stderr)
	}

	// 初始化组件
	pathManager := NewPathManager()
	backupManager := NewBackupManager()
	cleaner := &AugmentCleaner{
		PathManager:   pathManager,
		BackupManager: backupManager,
	}

	// 处理信息查询
	if showInfo {
		cleaner.ShowSystemInfo()
		return
	}

	if showCurrentIDs {
		cleaner.ShowCurrentIDs()
		return
	}

	// 验证参数
	if jetbrainsOnly && vscodeOnly {
		fmt.Println("错误: 不能同时指定 --jetbrains-only 和 --vscode-only")
		os.Exit(1)
	}

	// 开始清理过程
	fmt.Printf("=== %s v%s ===\n", AppName, Version)
	fmt.Println("开始AugmentCode设备限制清理...")
	fmt.Printf("配置: 备份=%t, 锁定=%t, 数据库清理=%t\n\n", createBackups, lockFiles, cleanDatabase)

	var totalSuccess = false

	// 处理JetBrains
	if !vscodeOnly {
		fmt.Println("🔧 处理JetBrains IDE...")
		jetbrainsResult := cleaner.ProcessJetBrainsIDEs(createBackups, lockFiles, cleanDatabase)

		if jetbrainsResult.Success {
			totalSuccess = true
			fmt.Printf("✅ JetBrains处理成功: %d个文件, %d条数据库记录\n",
				len(jetbrainsResult.FilesProcessed), jetbrainsResult.RecordsCleaned)

			if verbose {
				fmt.Println("  处理的文件:")
				for _, file := range jetbrainsResult.FilesProcessed {
					fmt.Printf("    - %s\n", file)
				}

				fmt.Println("  ID变更:")
				for file, oldID := range jetbrainsResult.OldIDs {
					newID := jetbrainsResult.NewIDs[file]
					fmt.Printf("    %s: %s -> %s\n", file, oldID, newID)
				}
			}
		} else {
			fmt.Printf("❌ JetBrains处理失败: %v\n", jetbrainsResult.Errors)
		}
		fmt.Println()
	}

	// 处理VSCode
	if !jetbrainsOnly {
		fmt.Println("🖥️ 处理VSCode系列...")
		vscodeResult := cleaner.ProcessVSCodeInstallations(createBackups, lockFiles)

		if vscodeResult.Success {
			totalSuccess = true
			fmt.Printf("✅ VSCode处理成功: %d个文件, %d条数据库记录\n",
				len(vscodeResult.FilesProcessed), vscodeResult.RecordsCleaned)

			if verbose {
				fmt.Println("  处理的文件:")
				for _, file := range vscodeResult.FilesProcessed {
					fmt.Printf("    - %s\n", file)
				}

				fmt.Println("  ID变更:")
				for key, oldID := range vscodeResult.OldIDs {
					newID := vscodeResult.NewIDs[key]
					fmt.Printf("    %s: %s -> %s\n", key, oldID, newID)
				}
			}
		} else {
			fmt.Printf("❌ VSCode处理失败: %v\n", vscodeResult.Errors)
		}
		fmt.Println()
	}

	// 总结
	if totalSuccess {
		fmt.Println("🎉 清理完成！")
		fmt.Println("📝 下一步:")
		fmt.Println("  1. 重启你的IDE（VSCode、JetBrains等）")
		fmt.Println("  2. 使用新的AugmentCode账户登录")
		fmt.Printf("  3. 备份文件保存在: %s\n", backupManager.BackupDir)

		if createBackups {
			fmt.Println("\n💡 如果遇到问题，可以从备份目录恢复原始文件")
		}
	} else {
		fmt.Println("❌ 清理失败，请检查错误信息")
		os.Exit(1)
	}
}

// 验证JetBrains安装
func (ac *AugmentCleaner) VerifyJetBrainsInstallation() bool {
	jetbrainsDir := ac.PathManager.GetJetBrainsConfigDir()
	if jetbrainsDir == "" {
		return false
	}

	// 检查关键目录
	requiredDirs := []string{"options", "config"}
	for _, dir := range requiredDirs {
		dirPath := filepath.Join(jetbrainsDir, dir)
		if _, err := os.Stat(dirPath); os.IsNotExist(err) {
			log.Printf("Missing required directory: %s", dirPath)
			return false
		}
	}

	// 检查是否有任何IDE配置目录
	files, err := os.ReadDir(jetbrainsDir)
	if err != nil {
		return false
	}

	ideCount := 0
	for _, file := range files {
		if file.IsDir() {
			// 检查是否是IDE目录（通常包含版本号）
			dirName := file.Name()
			if strings.Contains(dirName, "IntelliJ") ||
				strings.Contains(dirName, "PyCharm") ||
				strings.Contains(dirName, "WebStorm") ||
				strings.Contains(dirName, "GoLand") ||
				strings.Contains(dirName, "CLion") ||
				strings.Contains(dirName, "PhpStorm") ||
				strings.Contains(dirName, "RubyMine") ||
				strings.Contains(dirName, "DataGrip") ||
				strings.Contains(dirName, "Rider") ||
				strings.Contains(dirName, "AndroidStudio") {
				ideCount++
			}
		}
	}

	return ideCount > 0
}

// 验证VSCode安装
func (ac *AugmentCleaner) VerifyVSCodeInstallation() map[string]bool {
	results := make(map[string]bool)

	for _, variant := range vscodeConfig.Variants {
		variantPath := filepath.Join(ac.PathManager.ConfigDir, variant)
		if _, err := os.Stat(variantPath); err == nil {
			// 检查User目录
			userPath := filepath.Join(variantPath, "User")
			if _, err := os.Stat(userPath); err == nil {
				// 检查关键文件
				storageFile := filepath.Join(userPath, "storage.json")
				settingsFile := filepath.Join(userPath, "settings.json")

				hasStorage := false
				hasSettings := false

				if _, err := os.Stat(storageFile); err == nil {
					hasStorage = true
				}
				if _, err := os.Stat(settingsFile); err == nil {
					hasSettings = true
				}

				// 至少有一个关键文件存在就认为安装有效
				results[variant] = hasStorage || hasSettings
			} else {
				results[variant] = false
			}
		} else {
			results[variant] = false
		}
	}

	return results
}

// 获取安装信息摘要
func (ac *AugmentCleaner) GetInstallationSummary() map[string]interface{} {
	summary := make(map[string]interface{})

	// JetBrains安装状态
	jetbrainsInstalled := ac.VerifyJetBrainsInstallation()
	summary["jetbrains_installed"] = jetbrainsInstalled

	if jetbrainsInstalled {
		idFiles := ac.PathManager.GetJetBrainsIDFiles()
		existingFiles := 0
		for _, filePath := range idFiles {
			if _, err := os.Stat(filePath); err == nil {
				existingFiles++
			}
		}
		summary["jetbrains_id_files"] = existingFiles
		summary["jetbrains_total_files"] = len(idFiles)

		dbFiles := ac.PathManager.GetJetBrainsDatabaseFiles()
		summary["jetbrains_db_files"] = len(dbFiles)
	}

	// VSCode安装状态
	vscodeResults := ac.VerifyVSCodeInstallation()
	installedVariants := []string{}
	for variant, installed := range vscodeResults {
		if installed {
			installedVariants = append(installedVariants, variant)
		}
	}
	summary["vscode_variants"] = installedVariants
	summary["vscode_total_variants"] = len(vscodeConfig.Variants)

	return summary
}
