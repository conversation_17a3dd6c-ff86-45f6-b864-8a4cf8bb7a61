# 开发规范和规则

- 用户要求在 augment_cleaner.go 中新增清理逻辑：1) 检查用户先退出 Augment 账号登录状态 2) 清理 Windows 注册表中的 JetBrains 相关项 3) 清理 %APPDATA%\.jetbrains 目录 4) 清理用户目录下的 .augmentcode 目录。需要跨平台兼容、支持 dry-run 模式、独立方法实现、详细日志输出。
- 用户要求在 augment_cleaner.go 清理完成后添加 Augment API 域名测速和 hosts 优化功能：1) 测试 d1-d20.api.augmentcode.com 域名速度 2) 支持代理配置 3) 解析最快域名的IP并更新hosts文件 4) 跨平台兼容 5) 支持dry-run模式 6) 需要管理员权限处理
- 用户要求修改 augment_cleaner.go 中的 hosts 优化逻辑：1) 从测试结果中选择延迟最低的域名 2) 解析该最快域名的真实IP地址 3) 将所有域名（d1~d20.api.augmentcode.com）在hosts文件中都指向这个最快的IP地址，确保所有请求都路由到延迟最低的服务器
- 用户要求在 augment_cleaner.go 中添加全局错误处理和日志记录功能：1) 在main()函数中添加recover()机制捕获panic 2) 在系统临时目录创建日志文件，命名格式augment-cleaner-YYYYMMDD-HHMMSS.log 3) 记录程序运行详细信息、错误和调试信息 4) 包含时间戳、错误级别、程序版本、操作系统信息 5) 跨平台兼容性支持
- 用户要求修改程序成功完成逻辑：1) 在API优化成功后显示成功摘要（最快域名、IP地址、延迟、hosts文件修改状态、备份文件位置）2) 添加退出确认机制，显示"操作完成！按任意键退出..."并等待用户按键 3) 适用于所有成功完成场景 4) 不影响静默模式行为
- 用户关注hosts文件优化的准确性和可靠性：需要在添加新映射前清理现有条目，确保测试准确性不受现有hosts映射影响，提供备份和清理策略，改善用户体验
- 用户要求在 augment_cleaner.go 中新增对 IntelliJ IDEA 社区版的支持。根据 JetBrains 官方文档，专业版使用 "IntelliJIdea" 配置目录，社区版使用 "IdeaIC" 配置目录。需要在 jetbrainsProducts 列表中添加社区版条目，确保清理功能对两个版本都生效，并更新日志输出以区分专业版和社区版。
- 用户要求完善 isProcessRunning 函数实现真正的跨平台进程检查功能：1) Windows平台使用 tasklist 和 wmic 命令 2) Linux/macOS平台使用 pgrep 和 ps 命令 3) 设置10秒超时避免阻塞 4) 详细模式下记录进程检查信息 5) 错误处理返回false而不是报错 6) 支持多种检查方法作为备选方案
- 用户要求在 augment_cleaner.go 中新增 Cursor 和 VSCode 编辑器聊天记录清理功能：1) 清理所有工作区存储目录下的 Augment.vscode-augment 文件夹 2) 清理同级目录下的 state.vscdb 文件 3) 支持跨平台路径（Windows: C:\Users\<USER>\AppData\Roaming\Cursor\User\workspaceStorage\*\, macOS: ~/Library/Application Support/Cursor/User/workspaceStorage/*/） 4) 先备份再删除的策略 5) 集成到现有清理流程和菜单系统 6) 支持干运行模式和详细日志
- 版本号智能升级规则：当次版本号达到9时，主版本号+1，次版本号重置为0（如3.9→4.0）；否则次版本号+1（如3.8→3.9）
- 紧急安全修复完成：修复macOS进程终止逻辑的严重安全漏洞。1) 重写validateProcessMatch函数，添加isSystemCriticalProcess检查，特别保护com.apple.CodesigningHelper等系统进程；2) 修复checkProcessWithPgrep函数，移除危险的模糊匹配(.*%s.*)，使用-x参数进行精确匹配，添加isPgrepPatternSafe白名单验证；3) 增强checkProcessWithPs函数，添加系统进程预检查，实现isProcessLineMatch安全匹配；4) 新增多层安全验证：isSystemCriticalProcess、isPgrepPatternSafe、validatePgrepResult、isProcessLineMatch；5) 建立系统进程保护列表，包含com.apple.、/system/、codesigninghelper等关键进程；6) 对VSCode(code)进程实施特殊安全处理，避免误杀CodesigningHelper。所有进程检测现在使用精确匹配和多重安全验证，确保不会误杀系统关键进程。
