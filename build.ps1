# Augment清理工具构建脚本 - PowerShell版本

Write-Host "🧹 Augment清理工具 - 跨平台构建" -ForegroundColor Cyan
Write-Host "==================================" -ForegroundColor Cyan

# 版本管理
if (-not (Test-Path "version.txt")) {
    "2.0" | Out-File -FilePath "version.txt" -Encoding UTF8
    Write-Host "📝 创建初始版本文件: 2.0" -ForegroundColor Yellow
}

# 读取当前版本
$currentVersion = Get-Content "version.txt" -Raw
$currentVersion = $currentVersion.Trim()
Write-Host "📋 当前版本: $currentVersion" -ForegroundColor Cyan

# 计算新版本 (智能版本升级规则)
$versionParts = $currentVersion.Split('.')
$major = [int]$versionParts[0]
$minor = [int]$versionParts[1]

# 智能版本升级：当次版本号为9时，升级主版本号并重置次版本号为0
if ($minor -eq 9) {
    $newMajor = $major + 1
    $newMinor = 0
    $newVersion = "$newMajor.$newMinor"
    Write-Host "🔄 主版本升级: $currentVersion → $newVersion (次版本号达到9，升级主版本)" -ForegroundColor Magenta
} else {
    $newMinor = $minor + 1
    $newVersion = "$major.$newMinor"
    Write-Host "🔄 次版本升级: $currentVersion → $newVersion" -ForegroundColor Yellow
}

$newVersion | Out-File -FilePath "version.txt" -Encoding UTF8

# 检查Go环境
$goVersion = Get-Command go -ErrorAction SilentlyContinue
if (-not $goVersion) {
    Write-Host "❌ 未找到Go编译环境" -ForegroundColor Red
    Write-Host "请先安装Go: https://golang.org/dl/" -ForegroundColor Yellow
    exit 1
}

$goVersionOutput = go version
Write-Host "✅ 检测到Go编译环境: $goVersionOutput" -ForegroundColor Green

# 清空并创建构建目录
Write-Host "🗑️  清空构建目录..." -ForegroundColor Yellow
if (Test-Path "dist") {
    Remove-Item -Path "dist" -Recurse -Force
}
New-Item -ItemType Directory -Path "dist" | Out-Null

Write-Host "📦 开始编译清理工具 v$newVersion..." -ForegroundColor Yellow

# 构建目标平台
$platforms = @(
    # AMD64架构 (x86_64)
    @{OS = "windows"; ARCH = "amd64"},
    @{OS = "linux"; ARCH = "amd64"},
    @{OS = "darwin"; ARCH = "amd64"},
    
    # ARM64架构 (现代ARM设备)
    @{OS = "windows"; ARCH = "arm64"},
    @{OS = "linux"; ARCH = "arm64"},
    @{OS = "darwin"; ARCH = "arm64"},
    
    # ARM32架构 (嵌入式设备)
    @{OS = "linux"; ARCH = "arm"}
)

# 按架构分组构建
$archGroups = @{
    "amd64" = @{Name = "AMD64架构 (x86_64)"; Platforms = @()}
    "arm64" = @{Name = "ARM64架构 (现代ARM设备)"; Platforms = @()}
    "arm" = @{Name = "ARM32架构 (嵌入式设备)"; Platforms = @()}
}

# 将平台按架构分组
foreach ($platform in $platforms) {
    $archGroups[$platform.ARCH].Platforms += $platform
}

# 按组构建
foreach ($archType in @("amd64", "arm64", "arm")) {
    $group = $archGroups[$archType]
    if ($group.Platforms.Count -gt 0) {
        Write-Host ""
        Write-Host "🏗️ $($group.Name)" -ForegroundColor Magenta
        
        foreach ($platform in $group.Platforms) {
            $os = $platform.OS
            $arch = $platform.ARCH
            
            Write-Host "构建 $os/$arch..." -ForegroundColor White

            $outputName = "augment-cleaner-$os-$arch-v$newVersion"
            if ($os -eq "windows") {
                $outputName += ".exe"
            }
            
            # 设置环境变量并编译
            $env:GOOS = $os
            $env:GOARCH = $arch
            
            $buildResult = Start-Process -FilePath "go" -ArgumentList "build", "-o", "dist/$outputName", "augment_cleaner.go" -Wait -PassThru -NoNewWindow
            
            if ($buildResult.ExitCode -eq 0) {
                Write-Host "✅ $outputName" -ForegroundColor Green
            } else {
                Write-Host "❌ 构建失败: $outputName" -ForegroundColor Red
            }
        }
    }
}

Write-Host ""
Write-Host "📁 构建结果:" -ForegroundColor Cyan
Get-ChildItem -Path "dist" | Format-Table Name, Length, LastWriteTime

# 生成 Markdown 格式的发布说明
$markdownContent = @"
## 🎉 构建完成！版本: v$newVersion

### 📦 下载链接

#### AMD64架构 (x86_64)
- **Windows**: ``dist\augment-cleaner-windows-amd64-v$newVersion.exe``
- **Linux**: ``dist\augment-cleaner-linux-amd64-v$newVersion``
- **macOS**: ``dist\augment-cleaner-darwin-amd64-v$newVersion``

#### ARM64架构 (Apple Silicon, ARM服务器)
- **Windows**: ``dist\augment-cleaner-windows-arm64-v$newVersion.exe``
- **Linux**: ``dist\augment-cleaner-linux-arm64-v$newVersion``
- **macOS**: ``dist\augment-cleaner-darwin-arm64-v$newVersion``

#### ARM32架构 (树莓派等嵌入式设备)
- **Linux**: ``dist\augment-cleaner-linux-arm-v$newVersion``

> 💡 **提示**: Windows 用户可以直接双击 .exe 文件运行

### ⚠️ Unix 系统权限设置

在 macOS/Linux 上使用前，需要添加执行权限：

``````bash
chmod +x dist/augment-cleaner-linux-*
chmod +x dist/augment-cleaner-darwin-*
``````

### 📝 版本信息
- **当前版本**: v$newVersion
- **构建时间**: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
- **支持平台**: Windows, Linux, macOS (AMD64/ARM64/ARM32)
"@

Write-Host ""
Write-Host "🎉 构建完成！版本: v$newVersion" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Markdown 格式发布说明已生成，可直接复制用于发布:" -ForegroundColor Cyan
Write-Host "================================================================" -ForegroundColor Gray
Write-Host $markdownContent -ForegroundColor White
Write-Host "================================================================" -ForegroundColor Gray

# 保存 Markdown 内容到文件
$markdownContent | Out-File -FilePath "release-notes-v$newVersion.md" -Encoding UTF8
Write-Host ""
Write-Host "📄 发布说明已保存至: release-notes-v$newVersion.md" -ForegroundColor Yellow
